# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 7013
  ssl:
    enabled: true
    # 服务器的SSL证书文件路径
    key-store: classpath:ssl/ksun.chat_server.jks
    # 服务器的SSL证书密码
    key-store-password: Hm9!VKnRana$EDA2
    # 服务器的SSL证书类型
    key-store-type: JKS
  servlet:
    # 应用的访问路径
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 4
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 32

captcha:
  # 是否启用验证码校验
  enable: true
  # 验证码类型 math 数组计算 char 字符验证
  type: MATH
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# 日志配置
logging:
  level:
    org.dromara: @logging.level@
    org.springframework: warn
    org.mybatis.spring.mapper: error
    org.apache.fury: warn
  config: classpath:logback-plus.xml

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  application:
    name: RuoYi-Vue-Plus
  threads:
    # 开启虚拟线程 仅jdk21可用
    virtual:
      enabled: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    # 确保生产环境激活prod配置
    active: @profiles.active@
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  mvc:
    # 设置静态资源路径 防止所有请求都去查静态资源
    static-path-pattern: /static/**
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # jwt秘钥
  jwt-secret-key: pI12S5rqEfE8Ofq5knvEyiOM8oHoaNM8

# security配置
security:
  # 排除路径
  excludes:
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    - /favicon.ico
    - /error
    - /*/api-docs
    - /*/api-docs/**
    - /warm-flow-ui/config
    - /auth/login
    - /auth/register
    - /auth/tenantList
    - /auth/socialCallback
    - /auth/unlockSocial
    - /auth/refreshToken
    - /auth/logout
    - /member/login
    - /member/register
    - /member/check/username
    - /member/check/email
    - /member/logout
    - /payment/callback/**
    - /member/products/available
    - /api/sms/send
    - /member/phone-login
    - /api/proxy/**
    - /member/benefitConfig/detail
    - /auth/feishu/**

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 自定义配置 是否全局开启逻辑删除 关闭后 所有逻辑删除功能将失效
  enableLogicDelete: true
  # 多包名使用 例如 org.dromara.**.mapper,org.xxx.**.mapper
  mapperPackage: org.dromara.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: org.dromara.**.domain
  global-config:
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      # 如需改为自增 需要将数据库表全部设置为自增
      idType: ASSIGN_ID

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: false
  # 默认加密算法
  algorithm: BASE64
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password:
  # 公私钥 非对称算法的公私钥 如：SM2，RSA
  publicKey:
  privateKey:

# api接口加密
api-decrypt:
  # 是否开启全局接口加密
  enabled: true
  # AES 加密头标识
  headerFlag: encrypt-key
  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6axMt8b+fVaG8rqbSzjSEMJzd7InXRz2KbsnMb1M7bVTNyFkJSi3R7EiIZVtvyB1tkdmsxlemopUdljmYDFxljDf23cabN/x0bD4KIZLuJ/wCTbm2C2JjHoLXDIqm5any00zmvc5kakHP1YinfgApDKr4M+u6jC6pMnxUYj5o9hpp6Myebjarr8+cbRt9RKGdk8Ej6RPxrjQO9HDG3WvKdBm/VEe1TztOX8cTVvY5WLx4fQ0bTgAUZYg5gCipqKzHwAOYs+5PWkUwaWlafkWfzfJTbZ6hq1JXeK8os9N1Vq/NptF7O+5AcasvcPM8aybxQ4pCjXJUARALas+VVbJKQIDAQAB
  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==
  privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDprEy3xv59VobyuptLONIQwnN3siddHPYpuycxvUzttVM3IWQlKLdHsSIhlW2/IHW2R2azGV6ailR2WOZgMXGWMN/bdxps3/HRsPgohku4n/AJNubYLYmMegtcMiqblqfLTTOa9zmRqQc/ViKd+ACkMqvgz67qMLqkyfFRiPmj2GmnozJ5uNquvz5xtG31EoZ2TwSPpE/GuNA70cMbda8p0Gb9UR7VPO05fxxNW9jlYvHh9DRtOABRliDmAKKmorMfAA5iz7k9aRTBpaVp+RZ/N8lNtnqGrUld4ryiz03VWr82m0Xs77kBxqy9w8zxrJvFDikKNclQBEAtqz5VVskpAgMBAAECggEAJkTGh656TTKXhTcUabxTgtUlreuqULCGjA0PfTDgYWm5yCSazw8c/QvAaDmdsJhzTrfg1OIJwUt4S62BhS1zycIVV07pL/15D/tomNJtoj3nRMsOAUdr6GqwY5f/rF1++2eYVP+6GOXSvwevQam1CS8XQQkiP05dstEzyUOihreBbN200CRmKp9rAVp9tRImuM5KpLTNS45dvV01Rm6loq5ifnUZDjMUf5tE5NHqfOIgzHEiw/QAvBhqFWA9zppTgzekqjAK/HbdUytZ/t0SDwJpvTu8n/IlpmIQ/rEew73qda6zgLsmSmQwKzyv+GkI8StS/Fh6n3mxUuo1DV68gQKBgQD54kKeedoE63tPezO9+jhiy/3SiiNJdp5mVxUuRPGcbfwgVCHhQkuZaYoiDmDUxKY5I0H3El44Q/WSW40GWtFW1Bemv6OUyt9CFfLPo60trKcWZ+ZCk/rbJpNk4tTVsFGEFi1OEnnTTE/7i8QYJ6alIrev7YcoCwSMhUSbXKqdMQKBgQDvZHb7hPUtOe/16MCVqHjIH6SqYIkD0uPVfq00pxnDwMQZtEkJKi7S/zZwPPUqRYB0wQkH7M37eZ+91/xS7qBLfPHmjmF4dvp0n2w00YFWzPkCF/xlLans1PnrwVvqlpn7YXgf49ewSsOBSn95g/JrpUPjkebrsBXXRxjnoGcNeQKBgBOi4hVOAgHs902xoslFBnVNDm8jB87YEW59XV67cDkYs31e3yKycPpnPsGaW6WcpmtLB/4AZYMKZBvSMezT6itmk005NgOVRaIN03zZodw6+w5Zr9B/jHD4QRDmek8ogd9dVsYR0Zz3HDnqK8iyJPhJGCItCjftVbgtKM0EpSuhAoGBAKoTxViiuJG+JNaf2xs4iz8bBCEr3oxB6y5GBoZu5aYmsOvWI0GIn6W2PhX1qPS0VjQc5O6ldh5g5XW6GoFNGcMn7LCxnVw5o2zxyq2n3u0tSQgMxHuZuq0RDtJJGKWNA68OK4r0k8ujAk08AV7yuGt0CcyHXvk5q/q7f10on655AoGAJ2rZ3ghXVzDSziirqigLWe11ZafY7gWHZIENPeNmNznBRQjaoWmWJDmrrSxAMwmHNsE+X+muwNAjpYREAojX3qNeUPFGWZBj3S4ykwMrljEP5pd/XwE0+LayN1H/LjG6wEv334ODpGtU7jDxYMdp0GOzOZUH2FcXc4dYNbtVRVI=

springdoc:
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '标题：快升后台系统_接口文档'
    # 描述
    description: '描述：无'
    # 版本
    version: '版本号: ${project.version}'
    # 作者信息
    contact:
      name: tdsoft
      email:
      url:
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
        name: ${sa-token.token-name}
  #这里定义了两个分组，可定义多个，也可以不定义
  group-configs:
    - group: 2.通用模块
      packages-to-scan: org.dromara.web
    - group: 3.系统模块
      packages-to-scan: org.dromara.system
    - group: 4.代码生成模块
      packages-to-scan: org.dromara.generator
    - group: 5.工作流模块
      packages-to-scan: org.dromara.workflow

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludeUrls:
    - /system/notice
    - /product/info

# 全局线程池相关配置
# 如使用JDK21请直接使用虚拟线程 不要开启此配置
thread-pool:
  # 是否开启线程池
  enabled: false
  # 队列最大长度
  queueCapacity: 128
  # 线程池维护线程所允许的空闲时间
  keepAliveSeconds: 300

--- # 分布式锁 lock4j 全局配置
lock4j:
  # 获取分布式锁超时时间，默认为 3000 毫秒
  acquire-timeout: 3000
  # 分布式锁的超时时间，默认为 30 秒
  expire: 30000

--- # Actuator 监控端点的配置项
management:
  endpoints:
    access:
      default: none

--- # 默认/推荐使用sse推送
sse:
  enabled: true
  path: /resource/sse

--- # websocket
websocket:
  # 如果关闭 需要和前端开关一起关闭
  enabled: false
  # 路径
  path: /resource/websocket
  # 设置访问源地址
  allowedOrigins: '*'

--- # warm-flow工作流配置
warm-flow:
  # 是否开启工作流，默认true
  enabled: true
  # 是否开启设计器ui
  ui: true
  # 默认Authorization，如果有多个token，用逗号分隔
  token-name: ${sa-token.token-name},clientid
  # 流程状态对应的三元色
  chart-status-color:
    ## 未办理
    - 62,62,62
    ## 待办理
    - 255,205,23
    ## 已办理
    - 157,255,0

# ID生成器配置
id-generator:
  worker-id-bit-length: 10
  seq-bit-length: 12
  data-center-id: 1

# 扣费模块配置
billing:
  # 是否启用扣费模块
  enabled: true

  # Redis配置
  redis:
    # 分布式锁超时时间（毫秒）
    lock-timeout: 30000
    # 锁前缀
    lock-prefix: "billing:lock:"
    # 锁重试次数
    lock-retry-times: 3
    # 锁重试间隔（毫秒）
    lock-retry-interval: 100

  # 计费配置
  calculation:
    # 是否启用计费缓存
    cache-enabled: true
    # 缓存TTL（秒）
    cache-ttl: 300
    # 是否启用权益折扣
    benefit-discount-enabled: true
    # 是否启用免费额度
    free-quota-enabled: true
    # 默认计费模式
    default-billing-mode: "AUTO"

  # 异常配置
  exception:
    # 是否返回详细异常信息
    return-detail: false
    # 是否记录异常日志
    log-exception: true
    # 异常重试次数
    retry-times: 0

  # 缓存配置
  cache:
    # API令牌缓存TTL（秒）
    api-token-ttl: 60
    # 会员信息缓存TTL（秒）
    member-info-ttl: 300
    # 产品信息缓存TTL（秒）
    product-info-ttl: 600
    # 权益配置缓存TTL（秒）
    benefit-config-ttl: 1800

  # 日志配置
  logging:
    # 是否启用访问日志
    access-log-enabled: true
    # 是否启用扣费日志
    billing-log-enabled: true
    # 是否异步记录日志
    async-logging: true
    # 日志队列大小
    log-queue-size: 1000
