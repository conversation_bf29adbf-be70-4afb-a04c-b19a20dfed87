package org.dromara.member.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 会员积分批次对象 t_member_points_batch
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_member_points_batch")
public class TMemberPointsBatch extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 批次类型
     */
    private String batchType;

    /**
     * 来源ID
     */
    private String sourceId;

    /**
     * 总积分数
     */
    private Long totalPoints;

    /**
     * 剩余积分数
     */
    private Long remainingPoints;

    /**
     * 过期时间
     */
    private Date expireDate;

    /**
     * 状态
     */
    private String status;
}
