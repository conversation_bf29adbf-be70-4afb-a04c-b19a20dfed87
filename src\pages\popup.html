<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>多小宝（抖）</title>
    <link rel="stylesheet" href="../styles/popup.css" />
  </head>
  <body>
    <div class="container">
      <!-- 标题栏 -->
      <div class="header">
        <h1 class="title">
          <span class="icon">🎵</span>
          多小宝（抖）
          <span class="version">v1.0.2</span>
        </h1>
      </div>

      <!-- 标签导航 -->
      <div class="tabs">
        <button class="tab-btn active" data-tab="extract">获取</button>
        <button class="tab-btn" data-tab="settings">设置</button>
      </div>

      <!-- 获取页面 -->
      <div id="extract" class="tab-content active">
        <!-- 页面状态 - 弱化URL显示 -->
        <!-- <div class="page-info">
        <div class="page-status">
          <span class="page-type" id="pageType">检测中</span>
          <span class="status-description" id="statusDescription">已加载历史数据</span>
        </div>
      </div> -->

        <!-- 获取控制 -->
        <div class="extract-controls">
          <button id="extractBtn" class="btn primary">
            <span class="btn-icon">🚀</span>
            获取视频详情
          </button>
          <button id="stopBtn" class="btn stop" style="display: none">
            <span class="btn-icon">⏹️</span>
            停止获取
          </button>
          <button id="refreshPageStatusBtn" class="btn secondary" title="刷新页面状态检测">
            <span class="btn-icon">🔄</span>
            刷新状态
          </button>
        </div>

        <!-- 实时数据展示区域 - 根据页面类型动态显示 -->
        <div id="dataDisplay" class="data-display">
          <!-- 用户信息展示 (用户页面时显示) -->
          <div
            id="userInfoDisplay"
            class="user-info-display"
            style="display: none"
          >
            <div class="user-info-card">
              <div class="user-avatar">
                <img id="userAvatar" src="" alt="用户头像" />
              </div>
              <div class="user-details">
                <h3 id="userName"></h3>
                <div class="user-meta">
                  <span>抖音号：<span id="userDouyinId"></span></span>
                  <span>IP属地：<span id="userLocation"></span></span>
                </div>
                <div class="user-stats">
                  <span>关注：<span id="userFollowing"></span></span>
                  <span>粉丝：<span id="userFollowers"></span></span>
                  <span>获赞：<span id="userLikes"></span></span>
                  <span>作品：<span id="userWorks"></span></span>
                </div>
              </div>
            </div>
          </div>

          <!-- 作品列表展示 (用户页面时显示) -->
          <div
            id="videoListDisplay"
            class="video-list-display"
            style="display: none"
          >
            <div class="video-list-header">
              <h4>个人主页视频 (<span id="profile-video-count">0</span>)</h4>
              <div class="video-list-actions">
                <button
                  class="btn small"
                  data-action="export"
                  data-category="profile"
                  data-format="feishu"
                >
                  导到多维表格
                </button>
                <!-- <button
                  class="btn small"
                  data-action="export"
                  data-category="profile"
                  data-format="csv"
                >
                  导出CSV
                </button> -->
                <!-- <button
                  class="btn small"
                  data-action="export"
                  data-category="profile"
                  data-format="json"
                >
                  导出JSON
                </button> -->
                <button
                  class="btn small danger"
                  data-action="clear"
                  data-category="profile"
                >
                  清空
                </button>
              </div>
            </div>
            <div class="video-list-container">
              <div class="video-list" id="profile-video-list">
                <div class="empty-state">暂无个人主页视频数据</div>
              </div>
            </div>
          </div>

          <!-- 视频详情展示 (视频详情页时显示) -->
          <div
            id="videoDetailDisplay"
            class="video-detail-display"
            style="display: none"
          >
            <div class="video-detail-header">
              <h4>视频详情</h4>
            </div>
            <div class="video-detail-container">
              <div class="video-detail-info" id="video-detail-info">
                <!-- 视频详情信息将在这里显示 -->
              </div>
            </div>
          </div>

          <!-- 评论展示 (视频详情页时显示) -->
          <div
            id="commentDisplay"
            class="comment-display"
            style="display: none"
          >
            <div class="comment-header">
              <h4>评论 (<span id="comment-count">0</span>)</h4>
              <div class="comment-actions">
                <button
                  class="btn small"
                  data-action="export"
                  data-category="comment"
                  data-format="feishu"
                >
                  导到多维表格
                </button>
                <!-- <button
                  class="btn small"
                  data-action="export"
                  data-category="comment"
                  data-format="csv"
                >
                  导出CSV
                </button> -->
                <!-- <button
                  class="btn small"
                  data-action="export"
                  data-category="comment"
                  data-format="json"
                >
                  导出JSON
                </button> -->
                <button
                  class="btn small danger"
                  data-action="clear"
                  data-category="comment"
                >
                  清空
                </button>
              </div>
            </div>
            <div class="comment-container">
              <div class="comment-list" id="comment-list">
                <div class="empty-state">暂无评论数据</div>
              </div>
            </div>
          </div>

          <!-- 数据总览 (当没有特定页面类型时显示) -->
          <div id="dataOverview" class="data-overview" style="display: none">
            <!-- 数据分类 -->
            <div class="data-categories">
              <!-- 个人主页视频列表 -->
              <div class="data-category">
                <div class="category-header">
                  <h4>
                    个人主页视频 (<span id="profile-video-count-data">0</span>)
                  </h4>
                  <div class="category-actions">
                    <button
                      class="btn small"
                      data-action="export"
                      data-category="profile"
                      data-format="feishu"
                    >
                      导到多维表格
                    </button>
                    <!-- <button
                      class="btn small"
                      data-action="export"
                      data-category="profile"
                      data-format="csv"
                    >
                      导出CSV
                    </button> -->
                    <!-- <button
                      class="btn small"
                      data-action="export"
                      data-category="profile"
                      data-format="json"
                    >
                      导出JSON
                    </button> -->
                    <button
                      class="btn small danger"
                      data-action="clear"
                      data-category="profile"
                    >
                      清空
                    </button>
                  </div>
                </div>
                <div class="profile-video-container">
                  <div class="profile-video-list" id="profile-video-list-data">
                    <div class="empty-state">暂无个人主页视频数据</div>
                  </div>
                </div>
              </div>

              <!-- 评论数据（包含视频详情和评论） -->
              <div class="data-category">
                <div class="category-header">
                  <h4>评论数据 (<span id="comment-count-data">0</span>)</h4>
                  <div class="category-actions">
                    <button
                      class="btn small"
                      data-action="export"
                      data-category="comment"
                      data-format="feishu"
                    >
                      导到多维表格
                    </button>
                    <!-- <button
                      class="btn small"
                      data-action="export"
                      data-category="comment"
                      data-format="csv"
                    >
                      导出CSV
                    </button> -->
                    <!-- <button
                      class="btn small"
                      data-action="export"
                      data-category="comment"
                      data-format="json"
                    >
                      导出JSON
                    </button> -->
                    <button
                      class="btn small danger"
                      data-action="clear"
                      data-category="comment"
                    >
                      清空
                    </button>
                  </div>
                </div>
                <div class="comment-container">
                  <div class="comment-list" id="comment-list-data">
                    <div class="empty-state">暂无评论数据</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 全局操作 -->
            <div class="global-actions">
              <button class="btn danger" data-action="clear-all">
                清空所有数据
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 设置页面 -->
      <div id="settings" class="tab-content">
        <!-- API令牌设置 -->
        <div class="settings-section">
          <h3>🔑 API令牌设置</h3>

          <div class="setting-item">
            <label class="setting-label">API令牌:</label>
            <div class="token-input-container">
              <input
                type="password"
                id="apiTokenInput"
                placeholder="请输入API令牌"
                maxlength="64"
                class="token-input"
              />
              <button type="button" id="toggleTokenVisibility" class="toggle-visibility-btn" title="显示/隐藏令牌">
                👁️
              </button>
            </div>
            <div class="token-actions">
              <button id="saveTokenBtn" class="btn primary">保存</button>
              <button id="clearTokenBtn" class="btn danger">清除</button>
            </div>
          </div>

          <!-- API连接状态 -->
          <div class="setting-item">
            <div class="api-status">
              <div class="status-indicator">
                <div id="apiStatusIndicator" class="status-dot"></div>
                <span id="apiStatusText" class="status-text">检查中...</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 账户信息 -->
        <div class="settings-section" id="accountSection">
          <h3>👤 账户信息</h3>

          <div class="account-info">
            <div class="account-item">
              <span class="account-label">用户名:</span>
              <span class="account-value" id="accountUsername">-</span>
            </div>
            <div class="account-item">
              <span class="account-label">当前余额:</span>
              <span class="account-value balance-highlight" id="accountBalance">-- 点</span>
            </div>
          </div>

          <!-- 刷新账户信息按钮 -->
          <div class="setting-item">
            <button id="refreshAccountBtn" class="btn secondary">🔄 刷新账户信息</button>
          </div>
        </div>

        <!-- 版本更新设置 -->
        <div class="settings-section">
          <h3>🔄 版本更新</h3>

          <div class="setting-item">
            <div class="version-info-display">
              <div class="version-current">
                <span class="version-label">当前版本:</span>
                <span class="version-value" id="currentVersionDisplay">v1.0.2</span>
              </div>
              <div class="version-status">
                <span class="version-label">状态:</span>
                <span class="version-value" id="versionCheckStatus">检查中...</span>
              </div>
            </div>
          </div>

          <div class="setting-item">
            <button id="manualVersionCheckBtn" class="btn secondary">🔍 检查更新</button>
          </div>
        </div>

        <!-- 多维表格链接 -->
        <div class="settings-section">
          <h3>📊 多维表格链接</h3>

          <!-- 个人主页表格链接 -->
          <div class="setting-item">
            <label class="setting-label">
              个人主页表格URL:
              <input
                type="url"
                id="multidimensionalTableUrl"
                placeholder="请输入个人主页多维表格的URL地址"
                style="width: 100%; margin-top: 5px"
              />
            </label>
            <small
              style="
                color: #666;
                font-size: 11px;
                margin-top: 2px;
                display: block;
              "
              >用于导出个人主页视频数据到多维表格</small
            >
            
            <div style="margin-top: 8px;">
              <button id="getCurrentUrlBtn" class="btn secondary">🔗 获取当前链接</button>
             <small
              style="
                color: red;
                font-size: 11px;
                margin-top: 2px;
                display: block;
              "
              >请在对应的表格页面点击</small
            >
            </div>
          </div>

          <!-- 视频详情表格链接 -->
          <div class="setting-item" style="margin-top: 15px;">
            <label class="setting-label">
              视频详情表格URL:
              <input
                type="url"
                id="videoDetailTableUrl"
                placeholder="请输入视频详情多维表格的URL地址"
                style="width: 100%; margin-top: 5px"
              />
            </label>
            <small
              style="
                color: #666;
                font-size: 11px;
                margin-top: 2px;
                display: block;
              "
              >用于导出视频详情和评论数据到多维表格</small
            >
          
            <div style="margin-top: 8px;">
              <button id="getCurrentUrlBtnDetail" class="btn secondary">🔗 获取当前链接</button>
              <small
              style="
                color: red;
                font-size: 11px;
                margin-top: 2px;
                display: block;
              "
              >请在对应的表格页面点击</small
            >
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="../scripts/config.js"></script>
    <script src="../scripts/api-client.js"></script>
    <script src="../scripts/billing-manager.js"></script>
    <script src="../scripts/balance-display.js"></script>
    <script src="../scripts/version-checker.js"></script>
    <script src="../scripts/version-update-ui.js"></script>
    <script src="../scripts/popup.js"></script>
  </body>
</html>
