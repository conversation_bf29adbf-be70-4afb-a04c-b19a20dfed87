// 抖音数据获取器 - Popup脚本
class PopupManager {
  constructor() {
    this.currentTab = null;
    this.currentPageType = "unknown"; // 添加页面类型属性
    this.dataManager = new DataManager();
    this.billingManager = window.billingManager; // 扣费管理器（保持兼容性）

    // 新的权限管理
    this.authService = null;
    this.permissionGuard = null;
    this.platform = 'douyin';

    this.settings = {
      autoExtractVideos: true,
      autoExtractComments: true,
      autoExtractUsers: true,
      extractDelay: 1000,
      maxExtractCount: 50,
      includeTimestamp: true,
      includePageInfo: true,
      exportFormat: "json",
      multidimensionalTableUrl: "",
      videoDetailTableUrl: "",
      cozeAppUrl: "https://www.coze.cn/s/kVezcq3yQws/",
      //cozeAppUrl:"https://www.coze.cn/space/7509053468045295616/ui-builder-preview/7533047249015373850/pc/home",
      confirmBeforeBilling: true,
      serverUrl: "https://ksun.chat:7013",
    };

    this.init();
  }

  async init() {
    await this.loadSettings();
    await this.initializePermissionManagement();
    this.setupEventListeners();
    this.setupTabs();
    this.checkCurrentTab();
    this.setupMessageListener();
    this.setupTabChangeListener(); // 添加标签页变化监听
    this.setupAutoStateCheck(); // 启动自动状态检查
    await this.dataManager.loadAllData();
    await this.loadStoredData();
    this.updateAllDisplays();
    this.setupBillingManagement(); // 初始化扣费管理（保持兼容性）
  }

  // 初始化权限管理
  async initializePermissionManagement() {
    try {
      // 等待全局权限服务初始化
      if (window.authService) {
        this.authService = window.authService;
        await this.authService.initialize();
      }

      if (window.permissionGuard) {
        this.permissionGuard = window.permissionGuard;
      }

      console.log('抖音页面权限管理初始化成功');
    } catch (error) {
      console.error('权限管理初始化失败:', error);
    }
  }

  // 设置事件监听器
  setupEventListeners() {
    // 获取按钮
    document.getElementById("extractBtn").addEventListener("click", () => {
      this.extractData();
    });

    // 停止获取按钮
    const stopBtn = document.getElementById("stopBtn");
    if (stopBtn) {
      stopBtn.addEventListener("click", () => {
        this.stopExtraction();
      });
    }

    // 刷新账户信息按钮
    const refreshAccountBtn = document.getElementById("refreshAccountBtn");
    if (refreshAccountBtn) {
      refreshAccountBtn.addEventListener("click", () => {
        this.refreshAccountInfo();
      });
    }

    // 获取当前链接按钮（个人主页表格）
    const getCurrentUrlBtn = document.getElementById("getCurrentUrlBtn");
    if (getCurrentUrlBtn) {
      getCurrentUrlBtn.addEventListener("click", () => {
        this.getCurrentTabUrl('multidimensionalTableUrl');
      });
    }

    // 获取当前链接按钮（视频详情表格）
    const getCurrentUrlBtnDetail = document.getElementById("getCurrentUrlBtnDetail");
    if (getCurrentUrlBtnDetail) {
      getCurrentUrlBtnDetail.addEventListener("click", () => {
        this.getCurrentTabUrl('videoDetailTableUrl');
      });
    }

    // 刷新页面状态按钮
    const refreshPageStatusBtn = document.getElementById("refreshPageStatusBtn");
    if (refreshPageStatusBtn) {
      refreshPageStatusBtn.addEventListener("click", () => {
        this.refreshPageStatus();
      });
    }

    // 数据操作按钮的事件委托
    document.addEventListener("click", (event) => {
      const button = event.target.closest("button[data-action]");
      if (!button) return;

      const action = button.dataset.action;
      const category = button.dataset.category;
      const format = button.dataset.format;

      switch (action) {
        case "export":
          exportCategoryData(category, format);
          break;
        case "clear":
          clearCategoryData(category);
          break;
        case "clear-all":
          clearAllData();
          break;
      }
    });

    // 设置项监听
    this.setupSettingsListeners();
  }

  // 设置标签页
  setupTabs() {
    const tabBtns = document.querySelectorAll(".tab-btn");
    const tabContents = document.querySelectorAll(".tab-content");

    tabBtns.forEach((btn) => {
      btn.addEventListener("click", () => {
        const targetTab = btn.dataset.tab;

        // 移除所有活动状态
        tabBtns.forEach((b) => b.classList.remove("active"));
        tabContents.forEach((c) => c.classList.remove("active"));

        // 激活当前标签
        btn.classList.add("active");
        document.getElementById(targetTab).classList.add("active");

        // 如果切换到数据页面，刷新显示
        if (targetTab === "data") {
          this.updateAllDisplays();
        }
      });
    });
  }

  // 更新所有数据显示
  updateAllDisplays() {
    this.updateProfileVideoDisplay();
    this.updateDetailVideoDisplay();
    this.updateCommentDisplay();
    this.updateExtractPageDisplay(); // 新增：更新获取页面的数据显示
  }

  // 更新获取页面的数据显示
  updateExtractPageDisplay() {
    // 根据当前页面类型显示不同的数据
    if (this.currentPageType === "user") {
      this.showUserPageData();
    } else if (this.currentPageType === "video") {
      this.showVideoPageData();
    } else {
      this.showDataOverview();
    }
  }

  // 显示用户页面数据
  showUserPageData() {
    // 隐藏其他显示区域
    const videoDetailDisplay = document.getElementById("videoDetailDisplay");
    const commentDisplay = document.getElementById("commentDisplay");
    const dataOverview = document.getElementById("dataOverview");

    if (videoDetailDisplay) videoDetailDisplay.style.display = "none";
    if (commentDisplay) commentDisplay.style.display = "none";
    if (dataOverview) dataOverview.style.display = "none";

    // 显示用户信息和作品列表
    const userInfoDisplay = document.getElementById("userInfoDisplay");
    const videoListDisplay = document.getElementById("videoListDisplay");

    if (userInfoDisplay) userInfoDisplay.style.display = "block";
    if (videoListDisplay) videoListDisplay.style.display = "block";

    // 更新用户信息显示
    this.updateUserInfoDisplay();
    // 更新作品列表显示（复用现有逻辑）
    this.updateProfileVideoDisplayOnExtractPage();
  }

  // 显示视频页面数据
  showVideoPageData() {
    // 隐藏用户相关显示
    const userInfoDisplay = document.getElementById("userInfoDisplay");
    const videoListDisplay = document.getElementById("videoListDisplay");
    const dataOverview = document.getElementById("dataOverview");

    if (userInfoDisplay) userInfoDisplay.style.display = "none";
    if (videoListDisplay) videoListDisplay.style.display = "none";
    if (dataOverview) dataOverview.style.display = "none";

    // 显示视频详情和评论
    const videoDetailDisplay = document.getElementById("videoDetailDisplay");
    const commentDisplay = document.getElementById("commentDisplay");

    if (videoDetailDisplay) videoDetailDisplay.style.display = "block";
    if (commentDisplay) commentDisplay.style.display = "block";

    // 更新视频详情和评论显示
    this.updateVideoDetailDisplay();
    this.updateCommentDisplayOnExtractPage();
  }

  // 显示数据总览
  showDataOverview() {
    // 隐藏特定页面的显示
    const userInfoDisplay = document.getElementById("userInfoDisplay");
    const videoListDisplay = document.getElementById("videoListDisplay");
    const videoDetailDisplay = document.getElementById("videoDetailDisplay");
    const commentDisplay = document.getElementById("commentDisplay");
    const dataOverview = document.getElementById("dataOverview");

    if (userInfoDisplay) userInfoDisplay.style.display = "none";
    if (videoListDisplay) videoListDisplay.style.display = "none";
    if (videoDetailDisplay) videoDetailDisplay.style.display = "none";
    if (commentDisplay) commentDisplay.style.display = "none";
    if (dataOverview) dataOverview.style.display = "block";

    // 更新数据总览显示
    this.updateProfileVideoDisplay();
    this.updateCommentDisplay();
  }

  // 更新用户信息显示
  updateUserInfoDisplay() {
    // 从最新的用户资料中获取用户信息
    const latestUserData = this.dataManager.getLatestUserInfo();
    if (latestUserData && latestUserData.userInfo) {
      const userInfo = latestUserData.userInfo;

      // 更新用户信息显示
      const userAvatar = document.getElementById("userAvatar");
      const userName = document.getElementById("userName");
      const userDouyinId = document.getElementById("userDouyinId");
      const userLocation = document.getElementById("userLocation");
      const userFollowing = document.getElementById("userFollowing");
      const userFollowers = document.getElementById("userFollowers");
      const userLikes = document.getElementById("userLikes");
      const userWorks = document.getElementById("userWorks");

      if (userAvatar && userInfo.avatar) {
        userAvatar.src = userInfo.avatar;
        userAvatar.onerror = function () {
          this.style.display = "none";
        };
      }
      if (userName) userName.textContent = userInfo.name || "未知用户";
      if (userDouyinId)
        userDouyinId.textContent =
          userInfo.username || userInfo.douyinId || "未知";
      if (userLocation)
        userLocation.textContent = userInfo.ipLocation || "未知";
      if (userFollowing) userFollowing.textContent = userInfo.following || "0";
      if (userFollowers) userFollowers.textContent = userInfo.followers || "0";
      if (userLikes) userLikes.textContent = userInfo.totalLikes || "0";
      if (userWorks)
        userWorks.textContent = latestUserData.videos
          ? latestUserData.videos.length
          : "0";
    } else {
      // 没有找到用户信息数据时不显示任何信息
    }
  }

  // 更新获取页面的作品列表显示
  updateProfileVideoDisplayOnExtractPage() {
    const profileVideos = this.dataManager.getProfileVideos();
    const container = document.getElementById("profile-video-list");
    const countElement = document.getElementById("profile-video-count");

    if (countElement) countElement.textContent = profileVideos.length;
    if (!container) {
      return;
    }

    container.innerHTML = "";

    if (profileVideos.length === 0) {
      container.innerHTML =

        '<div class="empty-state">暂无个人主页视频数据</div>';
      return;
    }

    // 直接显示视频列表
    profileVideos.forEach((video, index) => {
      const item = this.createProfileVideoItem(video);
      container.appendChild(item);
    });
  }

  // 更新获取页面的评论显示
  updateCommentDisplayOnExtractPage() {
    const videoDetails = this.dataManager.getDetailVideos();
    const container = document.getElementById("comment-list");
    const countElement = document.getElementById("comment-count");

    // 收集所有评论
    let allComments = [];
    videoDetails.forEach((video) => {
      if (video.comments && Array.isArray(video.comments)) {
        // 为每条评论添加视频信息
        const commentsWithVideoInfo = video.comments.map((comment) => ({
          ...comment,
          videoTitle: video.videoInfo?.title || "未知视频",
          videoAuthor: video.videoInfo?.author?.name || "未知作者",
        }));
        allComments = allComments.concat(commentsWithVideoInfo);
      }
    });

    // 更新评论计数
    if (countElement) countElement.textContent = allComments.length;
    if (!container) return;

    container.innerHTML = "";

    if (allComments.length === 0) {
      container.innerHTML = '<div class="empty-state">暂无评论数据</div>';
      return;
    }

    // 显示评论列表（使用统一的带头像样式）
    allComments.forEach((comment) => {
      const item = this.createCommentItemWithAvatar(comment);
      container.appendChild(item);
    });
  }

  // 更新视频详情显示
  updateVideoDetailDisplay() {
    const videoDetails = this.dataManager.getDetailVideos();
    const container = document.getElementById("video-detail-info");

    if (!container) return;

    container.innerHTML = "";

    if (videoDetails.length === 0) {
      container.innerHTML = '<div class="empty-state">暂无视频详情数据</div>';
      return;
    }

    // 显示最新的视频详情
    const latestVideo = videoDetails[videoDetails.length - 1];
    if (latestVideo) {
      const item = this.createVideoDetailItem(latestVideo);
      container.appendChild(item);
    }
  }

  // 更新个人主页视频显示
  updateProfileVideoDisplay() {
    const profileVideos = this.dataManager.getProfileVideos();
    const container = document.getElementById("profile-video-list-data");
    const countElement = document.getElementById("profile-video-count-data");

    if (countElement) countElement.textContent = profileVideos.length;
    if (!container) return;

    container.innerHTML = "";

    if (profileVideos.length === 0) {
      container.innerHTML =
        '<div class="empty-state">暂无个人主页视频数据</div>';
      return;
    }

    profileVideos.forEach((video) => {
      const item = this.createProfileVideoItem(video);
      container.appendChild(item);
    });
  }

  // 更新详情视频显示
  updateDetailVideoDisplay() {
    const detailVideos = this.dataManager.getDetailVideos();
    const container = document.getElementById("detail-video-list");
    const countElement = document.getElementById("detail-video-count");

    if (countElement) countElement.textContent = detailVideos.length;
    if (!container) return;

    container.innerHTML = "";

    if (detailVideos.length === 0) {
      container.innerHTML = '<div class="empty-state">暂无详情视频数据</div>';
      return;
    }

    detailVideos.forEach((video) => {
      const item = this.createDetailVideoItem(video);
      container.appendChild(item);
    });
  }

  // 更新评论显示
  updateCommentDisplay() {
    const videoDetails = this.dataManager.getDetailVideos();
    const container = document.getElementById("comment-list-data");
    const countElement = document.getElementById("comment-count-data");

    // 收集所有评论
    let allComments = [];
    videoDetails.forEach((video) => {
      if (video.comments && Array.isArray(video.comments)) {
        // 为每条评论添加视频信息
        const commentsWithVideoInfo = video.comments.map((comment) => ({
          ...comment,
          videoTitle: video.videoInfo?.title || "未知视频",
          videoAuthor: video.videoInfo?.author?.name || "未知作者",
        }));
        allComments = allComments.concat(commentsWithVideoInfo);
      }
    });

    // 更新评论计数
    if (countElement) countElement.textContent = allComments.length;
    if (!container) return;

    container.innerHTML = "";

    if (allComments.length === 0) {
      container.innerHTML = '<div class="empty-state">暂无评论数据</div>';
      return;
    }

    // 创建评论列表标题
    const listHeader = document.createElement("div");
    listHeader.className = "comment-list-header";
    listHeader.innerHTML = `<h4>评论列表 (共${allComments.length}条)</h4>`;
    container.appendChild(listHeader);

    // 创建评论容器
    const commentsContainer = document.createElement("div");
    commentsContainer.className = "comments-container";

    // 添加所有评论（使用统一的带头像样式）
    allComments.forEach((comment) => {
      const commentItem = this.createCommentItemWithAvatar(comment);
      commentsContainer.appendChild(commentItem);
    });

    container.appendChild(commentsContainer);
  }

  // 创建个人主页视频项
  createProfileVideoItem(video) {
    const item = document.createElement("div");
    item.className = "profile-video-item";
    // 处理封面图
    const coverUrl = video.coverUrl || video.cover || "";
    const coverElement = coverUrl
      ? `<div class="video-cover"><img src="${coverUrl}" alt="视频封面" onerror="this.style.display='none'; this.parentElement.classList.add('error'); this.parentElement.innerHTML='无封面';"></div>`
      : `<div class="video-cover error">无封面</div>`;

    item.innerHTML = `
      ${coverElement}
      <div class="video-content">
        <div class="video-title">
          ${video.title || "无标题"}
          ${video.isTop ? '<span class="top-badge">📌 置顶</span>' : ""}
        </div>
        <div class="video-meta">
          <span>作者: ${video.author || "未知"}</span>
          <div class="interaction-badges">
            <span class="interaction-badge">👍 ${video.likes || "0"}</span>
            <span class="interaction-badge">⏱️ ${
              video.duration || "未知"
            }</span>
          </div>
        </div>
      </div>
    `;
    return item;
  }

  // 创建详情视频项
  createDetailVideoItem(video) {
    const item = document.createElement("div");
    item.className = "detail-video-item";

    // 处理封面图
    const coverUrl = video.coverUrl || video.cover || "";
    const coverElement = coverUrl
      ? `<div class="video-cover"><img src="${coverUrl}" alt="视频封面" onerror="this.style.display='none'; this.parentElement.classList.add('error'); this.parentElement.innerHTML='无封面';"></div>`
      : `<div class="video-cover error">无封面</div>`;

    item.innerHTML = `
      ${coverElement}
      <div class="video-content">
        <div class="video-title">${video.title || "无标题"}</div>
        <div><strong>作者:</strong> ${
          video.author?.name || video.author || "未知"
        }</div>
        <div class="interaction-badges">
          <span class="interaction-badge">👍 ${
            video.interactions?.likes || video.likes || "0"
          }</span>
          <span class="interaction-badge">💬 ${
            video.interactions?.comments || video.comments || "0"
          }</span>
          <span class="interaction-badge">📤 ${
            video.interactions?.shares || video.shares || "0"
          }</span>
          <span class="interaction-badge">⭐ ${
            video.interactions?.collects || video.collects || "0"
          }</span>
        </div>
        <div class="video-meta">
          <span>ID: ${video.videoId || video.id || "未知"}</span>
          <span>${new Date(video.extractedAt).toLocaleString()}</span>
        </div>
      </div>
    `;
    return item;
  }

  // 创建带头像的评论项（统一样式）
  createCommentItemWithAvatar(comment) {
    const item = document.createElement("div");
    item.className = "comment-item";

    // 构建评论HTML（带头像的统一样式）
    item.innerHTML = `
      <div class="comment-header">
        <div class="comment-user">
          ${
            comment.avatar
              ? `<img src="${comment.avatar}" alt="头像" class="comment-avatar">`
              : '<div class="comment-avatar-placeholder">U</div>'
          }
          <span class="comment-username">${
            comment.userName || "匿名用户"
          }</span>
        </div>
        <div class="comment-meta">
          ${
            comment.date
              ? `<span class="comment-date">${comment.date}</span>`
              : ""
          }
          ${
            comment.likes
              ? `<span class="comment-likes">👍 ${comment.likes}</span>`
              : ""
          }
          ${
            comment.location
              ? `<span class="comment-location">📍 ${comment.location}</span>`
              : ""
          }
        </div>
      </div>
      <div class="comment-content">${comment.content || "无内容"}</div>
      ${
        comment.videoTitle
          ? `
        <div class="comment-video-info">
          <span class="video-title-tag">视频：${comment.videoTitle}</span>
          <span class="video-author-tag">作者：${comment.videoAuthor}</span>
        </div>
      `
          : ""
      }
    `;

    return item;
  }

  // 创建视频详情项
  createVideoDetailItem(videoDetail) {
    const item = document.createElement("div");
    item.className = "video-detail-item";

    const videoInfo = videoDetail.videoInfo || {};
    const comments = videoDetail.comments || [];

    // 处理封面图
    const coverUrl = videoInfo.cover || "";
    const coverElement = coverUrl
      ? `<div class="video-cover"><img src="${coverUrl}" alt="视频封面" onerror="this.style.display='none'; this.parentElement.classList.add('error'); this.parentElement.innerHTML='无封面';"></div>`
      : `<div class="video-cover error">无封面</div>`;

    item.innerHTML = `
      <div class="video-detail-header">
        ${coverElement}
        <div class="video-detail-info">
          <h4 class="video-title">${videoInfo.title || "无标题"}</h4>
          <div class="video-author">
            <span>👤 ${videoInfo.author?.name || "未知作者"}</span>
            <span>📅 ${videoInfo.publishTime || "未知时间"}</span>
          </div>
          <div class="video-stats">
            <span>👍 ${videoInfo.stats?.likes || "0"}</span>
            <span>💬 ${videoInfo.stats?.comments || "0"}</span>
            <span>🔄 ${videoInfo.stats?.shares || "0"}</span>
            <span>⭐ ${videoInfo.stats?.collections || "0"}</span>
          </div>
          ${
            videoInfo.tags
              ? `<div class="video-tags">${videoInfo.tags}</div>`
              : ""
          }
          ${
            videoInfo.duration
              ? `<div class="video-duration">⏱️ ${videoInfo.duration}</div>`
              : ""
          }
        </div>
      </div>
      <div class="comment-section">
        <div class="comment-header">
          <h5>评论 (${comments.length} 条)</h5>
          <span class="extract-time">${new Date(
            videoDetail.extractedAt
          ).toLocaleString()}</span>
        </div>
        <div class="comment-items">
          ${
            comments.length > 0
              ? comments
                  .slice(0, 10)
                  .map(
                    (comment) => `
                <div class="comment-row">
                  <div class="comment-user">${comment.userName || "匿名"}</div>
                  <div class="comment-content">${comment.content}</div>
                  <div class="comment-meta">
                    <span>👍 ${comment.likes || "0"}</span>
                    <span>${comment.date || ""}</span>
                    ${
                      comment.location
                        ? `<span>📍 ${comment.location}</span>`
                        : ""
                    }
                  </div>
                </div>
              `
                  )
                  .join("")
              : '<div class="no-comments">暂无评论</div>'
          }
          ${
            comments.length > 10
              ? `<div class="more-comments">... 还有 ${
                  comments.length - 10
                } 条评论</div>`
              : ""
          }
        </div>
      </div>
    `;

    return item;
  }

  // 检查当前标签页
  async checkCurrentTab() {
    try {
      // 在侧栏环境中，需要获取当前窗口的活动标签页
      let tabs = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });

      // 如果没有找到标签页，尝试获取所有活动标签页
      if (!tabs || tabs.length === 0) {
        tabs = await chrome.tabs.query({ active: true });
      }

      this.currentTab = tabs[0];

      // 调试信息
      console.log('侧栏环境 - 当前标签页:', this.currentTab);

      // 检查是否找到了标签页
      if (!this.currentTab) {
        this.showError("无法获取当前标签页信息，请刷新页面后重试");
        this.currentPageType = "unknown";
        this.updateExtractPageDisplay();
        return;
      }

      // 检查是否为支持的网站
      const isDouyin = this.currentTab.url.includes("douyin.com");

      if (!isDouyin) {
        this.showError("请在抖音页面使用此扩展");
        this.currentPageType = "unknown";
        this.updateExtractPageDisplay();
        return;
      }

      // 优先使用PageStateManager获取实时页面状态
      try {
        const response = await chrome.tabs.sendMessage(this.currentTab.id, {
          action: 'getPageState'
        });

        if (response && response.success) {
          console.log('📍 从PageStateManager获取页面状态:', response);

          // 根据PageStateManager的状态更新UI
          if (response.pageType === 'user') {
            this.currentPageType = "user";
            this.setupUserPage();
          } else if (response.pageType === 'videoDetail') {
            this.currentPageType = "videoDetail";
            this.setupVideoDetailPage();
          } else {
            this.currentPageType = "unknown";
            this.updateStatus("error", "不支持的页面类型，请访问抖音用户页面或视频详情页");
          }

          this.updateExtractPageDisplay();
          return;
        }
      } catch (error) {
        console.log('⚠️ PageStateManager不可用，使用备用检测方法');
      }

      // 备用检测方法：直接检测页面上是否有视频播放器
      try {
        const checkResult = await chrome.scripting.executeScript({
          target: { tabId: this.currentTab.id },
          func: () => {
            // 检查是否有视频播放器
            const videoPlayerSelectors = [
              '[data-e2e="video-player"]',
              ".xgplayer",
              ".video-player-container",
              ".player-container",
              "video",
            ];

            for (const selector of videoPlayerSelectors) {
              if (document.querySelector(selector)) {
                return { hasVideoPlayer: true };
              }
            }

            return { hasVideoPlayer: false };
          },
        });

        const { hasVideoPlayer } = checkResult[0].result;

        if (hasVideoPlayer) {
          // 发现视频播放器，当作视频详情页处理
          this.currentPageType = "videoDetail";
          this.setupVideoDetailPage();
          this.updateExtractPageDisplay();
          return;
        }
      } catch (error) {
        // 继续检测其他页面类型
      }

      // 检测用户页面
      if (this.currentTab.url.includes("/user/")) {
        this.currentPageType = "user";
        this.setupUserPage();
        this.updateExtractPageDisplay();
        return;
      }

      // 都不是，显示错误
      this.currentPageType = "unknown";
      this.updateStatus(
        "error",
        "不支持的页面类型，请访问抖音用户页面或视频详情页"
      );
      this.updateExtractPageDisplay();
    } catch (error) {
      this.currentPageType = "unknown";
      this.showError("检查页面失败: " + error.message);
      this.updateExtractPageDisplay();
    }
  }

  updateStatus(type, message) {
    const statusElement = document.getElementById("status");
    const textElement = document.getElementById("statusText");

    if (statusElement) {
      statusElement.className = `status ${type}`;
    }

    // 特殊处理loading状态，确保文字不会旋转
    if (textElement) {
      if (type === "loading") {
        textElement.innerHTML = `<span class="loading"></span><span class="loading-text">${message}</span>`;
      } else {
        textElement.textContent = message;
      }
    }

    // 移除对已注释的statusDescription元素的引用
  }

  // 获取数据
  async extractData() {
    if (!this.currentTab) {
      this.updateStatus("error", "未找到活动标签页");
      return;
    }

    try {
      // 清空所有持久化数据
      await this.clearAllStoredData();

      // 显示停止按钮，隐藏开始按钮
      document.getElementById("extractBtn").style.display = "none";
      document.getElementById("stopBtn").style.display = "block";
      this.updateStatus("loading", "正在开始获取...");

      // 设置超时保障，确保按钮状态最终会恢复
      const buttonResetTimeout = setTimeout(() => {
        this.resetButtons();
        this.updateStatus("ready", "准备就绪");
      }, 120000); // 2分钟后自动恢复按钮状态

      // 保存timeout ID以便后续可以清除
      this.currentExtractionTimeout = buttonResetTimeout;

      // 根据当前页面类型执行不同的获取操作

      if (
        this.currentPageType === "videoDetail" ||
        document.querySelector("#extractBtn").dataset.action ===
          "extractVideoDetail"
      ) {
        // 视频详情页获取
        this.updateStatus("loading", "正在获取视频详情...");

        try {
          console.log('侧栏环境 - 尝试发送消息到标签页:', this.currentTab.id);

          const response = await new Promise((resolve) => {
            chrome.tabs.sendMessage(
              this.currentTab.id,
              {
                action: "extractVideoDetail",
                maxComments: 100,
              },
              (res) => {
                console.log('侧栏环境 - 收到响应:', res);
                console.log('侧栏环境 - runtime错误:', chrome.runtime.lastError);

                if (chrome.runtime.lastError) {
                  resolve({
                    success: false,
                    error:
                      "通信错误: " +
                      (chrome.runtime.lastError.message || "未知错误"),
                  });
                  return;
                }

                resolve(res || { success: false, error: "未收到响应" });
              }
            );
          });

          if (response && response.success) {
            this.updateStatus("loading", "正在获取视频详情...");
          } else {
            this.updateStatus("error", response?.error || "获取失败，请重试");
            this.resetButtons();
            clearTimeout(buttonResetTimeout);
          }
        } catch (err) {
          this.updateStatus(
            "error",
            "通信失败，请刷新页面重试: " + err.message
          );
          this.resetButtons();
          clearTimeout(buttonResetTimeout);
        }
      } else if (
        this.currentPageType === "user" ||
        document.querySelector("#extractBtn").dataset.action ===
          "extractUserData"
      ) {
        // 用户页面获取

        // 获取配置参数，提供默认值
        const maxScrollCountElement = document.getElementById("maxScrollCount");
        const maxScrolls = maxScrollCountElement
          ? parseInt(maxScrollCountElement.value) || 100
          : 100;

        try {
          console.log('侧栏环境 - 尝试发送用户数据获取消息到标签页:', this.currentTab.id);

          const response = await new Promise((resolve) => {
            chrome.tabs.sendMessage(
              this.currentTab.id,
              {
                action: "extractUserData",
                config: {
                  maxScrolls: maxScrolls,
                },
              },
              (res) => {
                console.log('侧栏环境 - 用户数据获取响应:', res);
                console.log('侧栏环境 - runtime错误:', chrome.runtime.lastError);

                if (chrome.runtime.lastError) {
                  resolve({
                    success: false,
                    error:
                      "通信错误: " +
                      (chrome.runtime.lastError.message || "未知错误"),
                  });
                  return;
                }

                resolve(res || { success: false, error: "未收到响应" });
              }
            );
          });

          if (response && response.success) {
            this.updateStatus(
              "loading",
              response.message || "正在获取用户数据..."
            );
          } else {
            this.updateStatus("error", response?.error || "获取失败，请重试");
            this.resetButtons();
            clearTimeout(buttonResetTimeout);
          }
        } catch (err) {
          this.updateStatus(
            "error",
            "通信失败，请刷新页面重试: " + err.message
          );
          this.resetButtons();
          clearTimeout(buttonResetTimeout);
        }
      } else {
        // 未知页面类型，尝试识别

        if (this.currentTab.url.includes("/user/")) {
          // 很可能是用户页面

          const maxScrolls = 100;
          try {
            const response = await new Promise((resolve) => {
              chrome.tabs.sendMessage(
                this.currentTab.id,
                {
                  action: "extractUserData",
                  config: { maxScrolls },
                },
                (res) => {
                  if (chrome.runtime.lastError) {
                    resolve({
                      success: false,
                      error: chrome.runtime.lastError.message,
                    });
                    return;
                  }
                  resolve(res || { success: false, error: "未收到响应" });
                }
              );
            });

            if (response && response.success) {
              this.updateStatus(
                "loading",
                response.message || "正在获取用户数据..."
              );
            } else {
              this.updateStatus("error", response?.error || "获取失败，请重试");
              this.resetButtons();
              clearTimeout(buttonResetTimeout);
            }
          } catch (err) {
            this.updateStatus("error", "通信失败，请刷新页面重试");
            this.resetButtons();
            clearTimeout(buttonResetTimeout);
          }
        } else if (
          this.currentTab.url.includes("/video/") ||
          this.currentTab.url.includes("modal_id=")
        ) {
          // 很可能是视频详情页

          try {
            const response = await new Promise((resolve) => {
              chrome.tabs.sendMessage(
                this.currentTab.id,
                {
                  action: "extractVideoDetail",
                  maxComments: 100,
                },
                (res) => {
                  if (chrome.runtime.lastError) {
                    resolve({
                      success: false,
                      error: chrome.runtime.lastError.message,
                    });
                    return;
                  }
                  resolve(res || { success: false, error: "未收到响应" });
                }
              );
            });

            if (response && response.success) {
              this.updateStatus("loading", "正在获取视频详情...");
            } else {
              this.updateStatus("error", response?.error || "获取失败，请重试");
              this.resetButtons();
              clearTimeout(buttonResetTimeout);
            }
          } catch (err) {
            this.updateStatus("error", "通信失败，请刷新页面重试");
            this.resetButtons();
            clearTimeout(buttonResetTimeout);
          }
        } else {
          this.updateStatus("error", "无法确定页面类型，请尝试刷新页面");
          this.resetButtons();
          clearTimeout(buttonResetTimeout);
        }
      }
    } catch (error) {
      this.updateStatus("error", "获取失败，请刷新页面重试: " + error.message);
      this.resetButtons();
      if (this.currentExtractionTimeout) {
        clearTimeout(this.currentExtractionTimeout);
      }
    }
  }

  // 清空所有持久化数据
  async clearAllStoredData() {
    try {
      // 清空所有保存的数据
      await new Promise((resolve) => {
        chrome.storage.local.remove(
          ["douyinData", "douyinVideoDetails"],
          () => {
            resolve();
          }
        );
      });

      // 重置数据管理器的内存数据
      this.dataManager.resetData();

      // 立即更新UI显示，确保清空数据后界面也同步清空
      this.updateAllDisplays();

      return true;
    } catch (error) {
      return false;
    }
  }

  // 停止获取
  async stopExtraction() {
    try {
      const extractBtn = document.getElementById("extractBtn");
      const pageType = extractBtn.dataset.pageType;

      if (pageType === "videoDetail") {
        // 视频详情页停止获取
        await chrome.tabs.sendMessage(this.currentTab.id, {
          action: "stopVideoDetailExtraction",
        });
      } else {
        // 个人主页停止获取 - 直接调用
        try {
          // 直接执行extractor.stopExtraction()
          await chrome.scripting.executeScript({
            target: { tabId: this.currentTab.id },
            func: () => {
              // 这个函数会在页面上下文中执行
              if (
                window.extractor &&
                typeof window.extractor.stopExtraction === "function"
              ) {
                window.extractor.stopExtraction();
                return { success: true };
              } else {
                return { success: false, error: "extractor对象不可用" };
              }
            },
          });
        } catch (err) {
          // 降级方案：如果直接调用失败，尝试使用消息传递
          await chrome.tabs.sendMessage(this.currentTab.id, {
            action: "stopAutoExtraction",
          });
        }
      }

      this.updateStatus("ready", "获取已停止");
      this.resetButtons();

      // 清除获取超时定时器
      if (this.currentExtractionTimeout) {
        clearTimeout(this.currentExtractionTimeout);
        this.currentExtractionTimeout = null;
      }

      // 重新检查页面状态以更新按钮
      await this.checkCurrentTab();
    } catch (error) {
      this.resetButtons();
      // 清除获取超时定时器
      if (this.currentExtractionTimeout) {
        clearTimeout(this.currentExtractionTimeout);
        this.currentExtractionTimeout = null;
      }
    }
  }

  // 重置按钮状态
  resetButtons() {
    document.getElementById("extractBtn").style.display = "block";
    document.getElementById("stopBtn").style.display = "none";
  }

  // 刷新账户信息
  async refreshAccountInfo() {
    // 这个方法将在统一权限管理器集成后实现
    console.log('刷新账户信息');
  }

  // 获取当前标签页URL并填充到指定的多维表格URL输入框
  async getCurrentTabUrl(targetInputId = 'multidimensionalTableUrl') {
    try {
      console.log('🔗 开始获取当前标签页URL，目标输入框:', targetInputId);

      // 获取当前活动标签页
      let tabs = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });

      // 如果没有找到标签页，尝试获取所有活动标签页
      if (!tabs || tabs.length === 0) {
        tabs = await chrome.tabs.query({ active: true });
      }

      if (!tabs || tabs.length === 0) {
        throw new Error('无法获取当前标签页信息');
      }

      const currentTab = tabs[0];
      const currentUrl = currentTab.url;

      if (!currentUrl) {
        throw new Error('当前标签页没有有效的URL');
      }

      console.log('✅ 获取到当前标签页URL:', currentUrl);

      // 验证URL格式
      const validationResult = this.validateFeishuTableUrl(currentUrl);
      if (!validationResult.isValid) {
        throw new Error(validationResult.message);
      }

      // 填充到指定的多维表格URL输入框
      const urlInput = document.getElementById(targetInputId);
      if (urlInput) {
        urlInput.value = currentUrl;

        // 触发input事件，确保任何监听器都能响应
        urlInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 保存到设置缓存中
        const settingKey = targetInputId === 'videoDetailTableUrl' ? 'videoDetailTableUrl' : 'multidimensionalTableUrl';
        await this.updateSetting(settingKey, currentUrl);

        // 显示成功提示
        this.showUrlCopySuccess(targetInputId);

        console.log('✅ URL已填充到输入框并保存到缓存');
      } else {
        throw new Error(`找不到多维表格URL输入框: ${targetInputId}`);
      }

    } catch (error) {
      console.error('❌ 获取当前链接失败:', error);
      this.showUrlCopyError(error.message, targetInputId);
    }
  }

  // 显示URL复制成功提示
  showUrlCopySuccess(targetInputId = 'multidimensionalTableUrl') {
    const buttonId = targetInputId === 'videoDetailTableUrl' ? 'getCurrentUrlBtnDetail' : 'getCurrentUrlBtn';
    const button = document.getElementById(buttonId);
    if (button) {
      const originalText = button.textContent;
      button.textContent = '✅ 链接已获取';
      button.style.background = '#4CAF50';

      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = '';
      }, 2000);
    }
  }

  // 显示URL复制错误提示
  showUrlCopyError(errorMessage, targetInputId = 'multidimensionalTableUrl') {
    const buttonId = targetInputId === 'videoDetailTableUrl' ? 'getCurrentUrlBtnDetail' : 'getCurrentUrlBtn';
    const button = document.getElementById(buttonId);
    if (button) {
      const originalText = button.textContent;
      button.textContent = '❌ 获取失败';
      button.style.background = '#f44336';

      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = '';
      }, 3000);
    }

    // 也可以显示详细错误信息
    console.error('获取当前链接详细错误:', errorMessage);

    // 可选：显示用户友好的错误提示
    if (typeof showMessage === 'function') {
      showMessage(`获取当前链接失败: ${errorMessage}`, 'error');
    }
  }

  // 验证导出前的多维表格链接是否已设置
  validateTableUrlBeforeExport(type) {
    let tableUrl = '';
    let tableType = '';

    if (type === 'profile') {
      tableUrl = this.settings.multidimensionalTableUrl || '';
      tableType = '个人主页';
    } else if (type === 'comment') {
      tableUrl = this.settings.videoDetailTableUrl || '';
      tableType = '视频详情';
    } else {
      // 默认使用个人主页表格URL
      tableUrl = this.settings.multidimensionalTableUrl || '';
      tableType = '个人主页';
    }

    if (!tableUrl || tableUrl.trim() === '') {
      return {
        isValid: false,
        message: `请先在设置页面配置${tableType}表格链接，然后再进行导出操作`
      };
    }

    return {
      isValid: true,
      message: '链接验证通过'
    };
  }

  // 验证飞书多维表格URL格式
  validateFeishuTableUrl(url) {
    if (!url || url.trim() === '') {
      return {
        isValid: false,
        message: '链接不能为空'
      };
    }

    try {
      const urlObj = new URL(url);

      // 检查是否为飞书域名
      if (!urlObj.hostname.includes('feishu.cn')) {
        return {
          isValid: false,
          message: '请使用正确的飞书多维表格链接'
        };
      }

      // 检查是否包含知识库路径（不支持）
      if (urlObj.pathname.includes('/wiki/')) {
        return {
          isValid: false,
          message: '暂不支持该知识库文档，需将模板迁移至云盘存储'
        };
      }

      // 检查是否包含table参数
      const searchParams = new URLSearchParams(urlObj.search);
      if (!searchParams.has('table')) {
        return {
          isValid: false,
          message: '请输入有效的飞书多维表格链接'
        };
      }

      return {
        isValid: true,
        message: '链接格式验证通过'
      };

    } catch (error) {
      return {
        isValid: false,
        message: '链接格式无效，请输入正确的URL地址'
      };
    }
  }

  // 显示验证错误的专用方法
  showValidationError(message, type = 'profile') {
    // 移除已存在的验证错误提示
    const existingValidationError = document.querySelector('.validation-error-modal');
    if (existingValidationError) {
      document.body.removeChild(existingValidationError);
    }

    const modal = document.createElement('div');
    modal.className = 'validation-error-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: fadeIn 0.3s ease-out;
    `;

    const tableType = type === 'profile' ? '个人主页' : '视频详情';
    const settingHint = type === 'profile' ?
      '请在设置页面的"个人主页表格URL"中配置链接' :
      '请在设置页面的"视频详情表格URL"中配置链接';

    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 12px;
      max-width: 500px;
      min-width: 400px;
      text-align: center;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      animation: slideInScale 0.3s ease-out;
    `;

    content.innerHTML = `
      <div style="color: #f44336; font-size: 48px; margin-bottom: 20px;">⚠️</div>
      <h3 style="color: #333; margin: 0 0 15px 0; font-size: 20px;">配置提醒</h3>
      <p style="color: #666; margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
        ${message}
      </p>
      <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196F3;">
        <p style="margin: 0; color: #555; font-size: 14px;">
          💡 <strong>操作提示：</strong><br>
          ${settingHint}
        </p>
      </div>
      <div style="display: flex; gap: 10px; justify-content: center;">
        <button id="goToSettings" style="
          background: #2196F3;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
        ">前往设置</button>
        <button id="closeValidationError" style="
          background: #6c757d;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
        ">知道了</button>
      </div>
    `;

    modal.appendChild(content);

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      @keyframes slideInScale {
        from {
          opacity: 0;
          transform: scale(0.8) translateY(-20px);
        }
        to {
          opacity: 1;
          transform: scale(1) translateY(0);
        }
      }
    `;
    if (!document.querySelector('#validation-error-styles')) {
      style.id = 'validation-error-styles';
      document.head.appendChild(style);
    }

    document.body.appendChild(modal);

    // 绑定事件
    const closeBtn = content.querySelector('#closeValidationError');
    const settingsBtn = content.querySelector('#goToSettings');

    const closeModal = () => {
      if (document.body.contains(modal)) {
        modal.style.animation = 'fadeOut 0.3s ease-in';
        setTimeout(() => {
          if (document.body.contains(modal)) {
            document.body.removeChild(modal);
          }
        }, 300);
      }
    };

    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModal();
    });

    settingsBtn.addEventListener('click', () => {
      // 切换到设置页面
      const settingsTab = document.querySelector('[data-tab="settings"]');
      if (settingsTab) {
        settingsTab.click();
      }
      closeModal();
    });

    // 添加fadeOut动画样式
    if (!document.querySelector('#fadeout-animation')) {
      const fadeOutStyle = document.createElement('style');
      fadeOutStyle.id = 'fadeout-animation';
      fadeOutStyle.textContent = `
        @keyframes fadeOut {
          from { opacity: 1; }
          to { opacity: 0; }
        }
      `;
      document.head.appendChild(fadeOutStyle);
    }
  }

  // 手动刷新页面状态
  async refreshPageStatus() {
    try {
      console.log('🔄 手动刷新页面状态');

      const button = document.getElementById('refreshPageStatusBtn');
      if (button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="btn-icon">⏳</span>刷新中...';
        button.disabled = true;

        // 先尝试强制重新检测页面类型
        if (this.currentTab && this.currentTab.url && this.currentTab.url.includes('douyin.com')) {
          try {
            await chrome.tabs.sendMessage(this.currentTab.id, {
              action: 'forceRedetect'
            });
          } catch (error) {
            // 强制检测失败，使用备用方法
          }
        }

        // 延迟后重新检查当前标签页状态
        setTimeout(async () => {
          await this.checkCurrentTab();
        }, 500);

        // 显示成功提示
        button.innerHTML = '<span class="btn-icon">✅</span>已刷新';

        setTimeout(() => {
          button.innerHTML = originalText;
          button.disabled = false;
        }, 2000);
      }

      console.log('✅ 页面状态刷新完成');

    } catch (error) {
      console.error('❌ 刷新页面状态失败:', error);

      const button = document.getElementById('refreshPageStatusBtn');
      if (button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="btn-icon">❌</span>刷新失败';

        setTimeout(() => {
          button.innerHTML = originalText;
          button.disabled = false;
        }, 3000);
      }

      if (typeof showMessage === 'function') {
        showMessage('刷新页面状态失败', 'error');
      }
    }
  }

  // 设置消息监听器
  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message);
    });
  }

  // 设置自动状态检查
  setupAutoStateCheck() {
    // 每3秒检查一次页面状态
    this.autoCheckInterval = setInterval(async () => {
      if (this.currentTab && this.currentTab.url && this.currentTab.url.includes('douyin.com')) {
        try {
          const response = await chrome.tabs.sendMessage(this.currentTab.id, {
            action: 'getPageState'
          });

          if (response && response.success) {
            const newPageType = response.pageType;

            // 如果页面类型发生变化，更新UI
            if (newPageType !== this.currentPageType) {
              console.log(`🔄 检测到页面类型变化: ${this.currentPageType} -> ${newPageType}`);

              if (newPageType === 'user') {
                this.currentPageType = "user";
                this.setupUserPage();
              } else if (newPageType === 'videoDetail') {
                this.currentPageType = "videoDetail";
                this.setupVideoDetailPage();
              } else {
                this.currentPageType = "unknown";
                this.updateStatus("error", "不支持的页面类型");
              }

              this.updateExtractPageDisplay();
            }
          }
        } catch (error) {
          // 忽略通信错误，可能是页面还未加载完成
        }
      }
    }, 3000);

    console.log('⏰ 自动状态检查已启动');
  }

  // 停止自动状态检查
  stopAutoStateCheck() {
    if (this.autoCheckInterval) {
      clearInterval(this.autoCheckInterval);
      this.autoCheckInterval = null;
      console.log('⏹️ 自动状态检查已停止');
    }
  }

  // 设置标签页变化监听器
  setupTabChangeListener() {
    // 监听标签页激活事件
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      console.log('🔄 标签页激活变化:', activeInfo);
      // 延迟一点时间确保页面加载完成
      setTimeout(async () => {
        await this.checkCurrentTab();
      }, 500);
    });

    // 监听标签页更新事件（URL变化）
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && changeInfo.url) {
        console.log('🔄 标签页URL更新:', changeInfo.url);
        // 延迟一点时间确保页面加载完成
        setTimeout(async () => {
          await this.checkCurrentTab();
        }, 1000);
      }
    });

    // 监听窗口焦点变化
    chrome.windows.onFocusChanged.addListener(async (windowId) => {
      if (windowId !== chrome.windows.WINDOW_ID_NONE) {
        console.log('🔄 窗口焦点变化:', windowId);
        // 延迟一点时间确保窗口切换完成
        setTimeout(async () => {
          await this.checkCurrentTab();
        }, 300);
      }
    });

    console.log('✅ 标签页变化监听器已设置');
  }

  handleMessage(message) {
    switch (message.type) {
      case "DATA_EXTRACTION_PROGRESS":
        // 处理获取进度更新
        this.handleExtractionProgress(message.data);
        break;

      case "DATA_EXTRACTED":
        // 处理获取完成
        this.handleDataExtracted(message.data);
        break;

      case "DATA_EXTRACTION_ERROR":
        // 处理获取错误
        this.handleExtractionError(message.error);
        break;

      case "VIDEO_DETAIL_EXTRACTION_PROGRESS":
        // 处理视频详情获取进度
        this.handleVideoDetailProgress(message.data);
        break;

      case "VIDEO_DETAIL_EXTRACTED":
        // 处理视频详情数据接收，立即更新UI
        if (message.data) {
          this.dataManager.addDetailVideo(message.data);
          this.updateAllDisplays();
        }
        break;

      case "VIDEO_DETAIL_EXTRACTION_COMPLETE":
        // 处理视频详情获取完成
        this.handleVideoDetailComplete(message.data);
        break;

      case "EXTRACTION_COMPLETE":
        // 处理常规获取完成
        this.handleExtractionComplete(message.data);
        break;

      case "EXTRACTION_FINISHED":
        // 处理获取完成，专门用于更新按钮状态
        this.updateStatus("success", message.message || "获取已完成");
        this.resetButtons();

        // 清除获取超时定时器
        if (this.currentExtractionTimeout) {
          clearTimeout(this.currentExtractionTimeout);
          this.currentExtractionTimeout = null;
        }

        // 获取完成后，立即重新加载数据并更新UI
        this.reloadDataAndUpdateUI();
        break;

      case "PAGE_STATE_CHANGED":
        // 处理页面状态变化
        this.handlePageStateChanged(message);
        break;

      default:
        break;
    }
  }

  // 处理页面状态变化
  handlePageStateChanged(message) {
    const { pageType } = message;

    if (pageType !== this.currentPageType) {
      console.log(`🔄 收到页面状态变化通知: ${this.currentPageType} -> ${pageType}`);

      if (pageType === 'user') {
        this.currentPageType = "user";
        this.setupUserPage();
      } else if (pageType === 'videoDetail') {
        this.currentPageType = "videoDetail";
        this.setupVideoDetailPage();
      } else {
        this.currentPageType = "unknown";
        this.updateStatus("error", "不支持的页面类型");
      }

      this.updateExtractPageDisplay();
    }
  }

  // 获取完成后重新加载数据并更新UI
  async reloadDataAndUpdateUI() {
    try {
      // 重新加载所有数据
      await this.dataManager.loadAllData();
      // 更新UI显示
      this.updateAllDisplays();
    } catch (error) {
      // 重新加载数据时出现错误，但不影响其他功能
    }
  }

  // 处理进度更新
  handleExtractionProgress(progressData) {
    const { newVideos, totalCount, maxCount, progress } = progressData;

    // 更新状态显示
    this.updateStatus(
      "loading",
      `获取进度: ${totalCount}/${maxCount} (${progress}%)`
    );

    // 更新数据管理器
    this.dataManager.updateData(progressData);

    // 实时更新显示
    this.updateAllDisplays();
  }

  // 处理视频详情获取进度
  handleVideoDetailProgress(progressData) {
    const { currentCount, totalEstimated, progress } = progressData;
    this.updateStatus(
      "loading",
      `获取进度: ${currentCount}/${totalEstimated || "未知"} (${
        progress || 0
      }%)`
    );

    // 尝试实时更新UI
    this.updateAllDisplays();
  }

  // 处理获取完成
  handleDataExtracted(finalData) {
    this.dataManager.updateData(finalData);
    this.updateAllDisplays();

    this.updateStatus(
      "success",
      `获取完成! 共获取 ${finalData.totalCount} 条数据`
    );

    // 重置按钮状态
    this.resetButtons();
  }

  // 处理获取错误
  handleExtractionError(error) {
    this.updateStatus("error", `获取失败，请重试: ${error.message}`);
    this.resetButtons();
  }

  // 处理视频详情获取完成
  handleVideoDetailComplete(data) {
    // 添加到DataManager
    this.dataManager.addDetailVideo(data);
    this.updateAllDisplays();

    // 显示成功消息
    this.updateStatus(
      "success",
      `视频详情获取完成！获取到${data.comments?.length || 0}条评论`
    );

    // 重置按钮状态
    this.resetButtons();

    // 重新检查页面状态以更新按钮
    this.checkCurrentTab();

    showMessage("视频详情获取完成！", "success");
  }

  // 导出数据
  async exportData(format) {
    if (!this.dataManager.hasData()) {
      alert("没有可导出的数据");
      return;
    }

    const data = this.prepareExportData();
    const timestamp = new Date()
      .toISOString()
      .slice(0, 19)
      .replace(/[:-]/g, "");

    let content, filename, mimeType;

    switch (format) {
      case "json":
        content = JSON.stringify(data, null, 2);
        filename = `douyin_data_${timestamp}.json`;
        mimeType = "application/json";
        break;

      case "csv":
        content = this.convertToCSV(data);
        filename = `douyin_data_${timestamp}.csv`;
        mimeType = "text/csv;charset=utf-8;";
        break;

      default:
        alert("不支持的导出格式");
        return;
    }

    this.downloadFile(content, filename, mimeType);
  }

  // 准备导出数据
  prepareExportData() {
    const data = { ...this.dataManager.getData() };

    if (!this.settings.includeTimestamp) {
      delete data.timestamp;
    }

    if (!this.settings.includePageInfo) {
      delete data.currentUrl;
      delete data.pageType;
    }

    return data;
  }

  // 转换为CSV格式
  convertToCSV(data) {
    let csv = "";

    // 添加 BOM 头，解决中文乱码问题
    csv = "\uFEFF";

    // 视频数据
    if (data.videos && data.videos.length > 0) {
      csv +=
        "视频ID,标题,作者,时长,发布时间,点赞数,视频链接,封面链接,获取时间\n";

      data.videos.forEach((video) => {
        // 处理字段，确保CSV格式正确
        const id = this.escapeCsvField(video.id || "");
        const title = this.escapeCsvField(video.title || "");
        const author = this.escapeCsvField(video.author || "");
        const duration = this.escapeCsvField(video.duration || "");
        const publishTime = this.escapeCsvField(video.publishTime || "");
        const likes = this.escapeCsvField(video.likes || "0");
        const videoUrl = this.escapeCsvField(video.videoUrl || "");
        const coverUrl = this.escapeCsvField(video.coverUrl || "");
        const extractedAt = this.escapeCsvField(video.extractedAt || "");

        csv += `${id},${title},${author},${duration},${publishTime},${likes},${videoUrl},${coverUrl},${extractedAt}\n`;
      });
      csv += "\n";
    }

    // 评论数据（如果有）
    if (data.comments && data.comments.length > 0) {
      csv += "评论ID,评论作者,评论内容,点赞数,发布时间\n";
      data.comments.forEach((comment) => {
        const id = this.escapeCsvField(comment.id || "");
        const author = this.escapeCsvField(comment.author || "");
        const content = this.escapeCsvField(comment.content || "");
        const likes = this.escapeCsvField(comment.likes || "0");
        const publishTime = this.escapeCsvField(comment.publishTime || "");

        csv += `${id},${author},${content},${likes},${publishTime}\n`;
      });
      csv += "\n";
    }

    // 用户信息（如果有）
    if (data.userInfo && Object.keys(data.userInfo).length > 0) {
      csv += "用户信息字段,值\n";
      Object.entries(data.userInfo).forEach(([key, value]) => {
        const fieldName = this.escapeCsvField(key);
        const fieldValue = this.escapeCsvField(value || "");
        csv += `${fieldName},${fieldValue}\n`;
      });
    }

    return csv;
  }

  // CSV字段转义处理
  escapeCsvField(field) {
    if (field === null || field === undefined) {
      return "";
    }

    // 转换为字符串
    let str = String(field);

    // 如果包含逗号、引号、换行符，需要用引号包围并转义内部引号
    if (
      str.includes(",") ||
      str.includes('"') ||
      str.includes("\n") ||
      str.includes("\r")
    ) {
      str = '"' + str.replace(/"/g, '""') + '"';
    }

    return str;
  }

  // 下载文件
  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);

    chrome.downloads
      .download({
        url: url,
        filename: filename,
        saveAs: true,
      })
      .then(() => {
        URL.revokeObjectURL(url);
      })
      .catch((error) => {
        showMessage("文件下载失败", "error");
      });
  }

  // 加载本地存储的数据
  async loadStoredData() {
    try {
      const result = await chrome.storage.local.get(["douyinData"]);
      const storedData = result.douyinData || [];

      if (storedData.length > 0) {
        // 使用最新的一条数据
        const latestData = storedData[storedData.length - 1];
        this.dataManager.updateData(latestData);

        // 更新显示
        this.updateAllDisplays();

        // 更新状态
        this.updateStatus("success", `已加载历史数据`);
      } else {
        this.updateStatus("waiting", "暂无历史数据");
      }
    } catch (error) {
      this.updateStatus("warning", "加载历史数据失败");
    }
  }

  // 设置设置项监听器
  setupSettingsListeners() {
    const settingIds = [
      "multidimensionalTableUrl",
      "videoDetailTableUrl",
      "cozeAppUrl"
    ];

    settingIds.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener("change", () => {
          this.updateSetting(id, this.getElementValue(element));
        });
      }
    });



    // 版本检查监听器
    this.setupVersionCheckListeners();
  }

  // 设置版本检查相关的事件监听器
  setupVersionCheckListeners() {
    // 手动检查更新按钮
    const manualVersionCheckBtn = document.getElementById("manualVersionCheckBtn");
    if (manualVersionCheckBtn) {
      manualVersionCheckBtn.addEventListener("click", async () => {
        manualVersionCheckBtn.disabled = true;
        manualVersionCheckBtn.textContent = "🔄 检查中...";

        try {
          console.log('🔍 手动检查版本更新...');

          if (window.versionChecker) {
            const result = await window.versionChecker.manualCheck();
            if (result) {
              if (result.hasUpdate) {
                showMessage(`发现新版本 ${result.latestVersion}`, "warning");
              } else {
                showMessage("当前已是最新版本", "success");
              }
            } else {
              showMessage("版本检查失败", "error");
            }
          } else {
            showMessage("版本检查器未初始化", "error");
          }
        } catch (error) {
          console.error('手动检查版本失败:', error);
          showMessage("检查失败: " + error.message, "error");
        } finally {
          manualVersionCheckBtn.disabled = false;
          manualVersionCheckBtn.textContent = "🔍 检查更新";
        }
      });
    }

    // 监听版本状态更新事件
    document.addEventListener('versionStatusUpdate', (event) => {
      this.handleVersionStatusUpdate(event.detail);
    });

    // 初始化版本显示
    this.initVersionDisplay();
  }

  // 初始化版本显示
  initVersionDisplay() {
    try {
      // 更新当前版本显示
      const currentVersionDisplay = document.getElementById("currentVersionDisplay");
      if (currentVersionDisplay) {
        const manifest = chrome.runtime.getManifest();
        currentVersionDisplay.textContent = `v${manifest.version}`;
      }

      // 初始化状态
      const versionCheckStatus = document.getElementById("versionCheckStatus");
      if (versionCheckStatus) {
        versionCheckStatus.textContent = "就绪";
        versionCheckStatus.style.color = "#64748b";
      }

    } catch (error) {
      console.error('初始化版本显示失败:', error);
    }
  }

  // 处理版本状态更新
  handleVersionStatusUpdate(versionData) {
    console.log('📊 处理版本状态更新:', versionData);

    const versionCheckStatus = document.getElementById("versionCheckStatus");
    if (versionCheckStatus) {
      if (versionData.hasUpdate) {
        versionCheckStatus.textContent = `有新版本 ${versionData.latestVersion}`;
        versionCheckStatus.style.color = "#f59e0b";
      } else {
        versionCheckStatus.textContent = "已是最新版本";
        versionCheckStatus.style.color = "#10b981";
      }
    }
  }





  // 获取元素值
  getElementValue(element) {
    if (element.type === "checkbox") {
      return element.checked;
    } else if (element.type === "number") {
      return parseInt(element.value);
    } else {
      return element.value;
    }
  }

  // 更新设置
  async updateSetting(key, value) {
    this.settings[key] = value;
    try {
      await chrome.storage.sync.set({
        douyinExtractorSettings: this.settings,
      });
    } catch (error) {
      // 设置保存失败，不影响主要功能
    }
  }



  // 加载设置
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get("douyinExtractorSettings");
      if (result.douyinExtractorSettings) {
        this.settings = { ...this.settings, ...result.douyinExtractorSettings };
      }
      this.applySettings();
    } catch (error) {
      // 加载设置失败，使用默认设置
    }
  }

  // 应用设置
  applySettings() {
    Object.entries(this.settings).forEach(([key, value]) => {
      const element = document.getElementById(key);
      if (element) {
        if (element.type === "checkbox") {
          element.checked = value;
        } else {
          element.value = value;
        }
      }
    });
  }

  // 设置视频详情页
  setupVideoDetailPage() {
    const extractBtn = document.getElementById("extractBtn");

    if (extractBtn) {
      extractBtn.innerHTML = '<span class="btn-icon">🚀</span>获取视频详情';
      // 使用渐变色主题，匹配顶部的主题色
      extractBtn.style.background = "linear-gradient(90deg, #f14668, #f46a6a)";
      extractBtn.style.border = "none";
      extractBtn.style.boxShadow = "0 2px 8px rgba(241, 70, 104, 0.3)";
      extractBtn.dataset.pageType = "videoDetail";
      extractBtn.dataset.action = "extractVideoDetail";
      extractBtn.disabled = false;
    }

    this.updateStatus("ready", "已连接到抖音视频详情页");
  }

  // 设置用户页面
  setupUserPage() {
    const extractBtn = document.getElementById("extractBtn");

    if (extractBtn) {
      extractBtn.innerHTML = '<span class="btn-icon">🚀</span>获取用户视频';
      // 使用渐变色主题，匹配顶部的主题色
      extractBtn.style.background = "linear-gradient(90deg, #f14668, #f46a6a)";
      extractBtn.style.border = "none";
      extractBtn.style.boxShadow = "0 2px 8px rgba(241, 70, 104, 0.3)";
      extractBtn.dataset.pageType = "user";
      extractBtn.dataset.action = "extractUserData";
      extractBtn.disabled = false;
    }

    this.updateStatus("ready", "已连接到抖音用户页面");
  }

  // 导到多维表格（飞书授权弹窗 + 原有扣费流程）
  async exportToFeiShu(domain, type, data) {
    // 防重复调用保护
    if (this._isExportingToFeiShu) {
      console.warn("飞书导出正在进行中，忽略重复调用");
      return;
    }

    // 1. 导出前的链接验证
    const linkValidationResult = this.validateTableUrlBeforeExport(type);
    if (!linkValidationResult.isValid) {
      this.showValidationError(linkValidationResult.message, type);
      return;
    }

    this._isExportingToFeiShu = true;

    try {
      console.log('🚀 开始飞书导出流程（授权弹窗 + 后台扣费）');

      // 1. 权限和扣费检查（保持原有逻辑）
      if (this.permissionGuard) {
        const permissionResult = await this.permissionGuard.checkPermissionAndBilling(
          'exportToFeiShu',
          this.platform
        );

        if (!permissionResult.success) {
          console.error('飞书导出权限检查失败:', permissionResult.error);
          return;
        }
      }
      // 兼容旧的扣费管理器
      else if (this.billingManager) {
        // 先显示扣费确认弹窗
        const canProceed = await this.billingManager.showBillingPrompt("exportToFeiShu");
        if (!canProceed) {
          return; // 用户取消操作
        }

        // 检查余额
        try {
          const productId = this.billingManager.getProductId();
          const balanceCheck = await this.billingManager.checkBalance(productId, 1);
          if (!balanceCheck.sufficient) {
            showMessage(`点数不足，无法导出。当前余额：${balanceCheck.currentBalance}，需要：${balanceCheck.requiredPoints}`, "error");
            return;
          }
          console.log("点数检查通过，准备进行飞书授权");
        } catch (error) {
          console.error("点数检查失败:", error);
          showMessage("点数检查失败: " + error.message, "error");
          return;
        }
      }

      // 2. 扣费成功后，直接打开Coze环境
      console.log('✅ 扣费检查通过，开始集成Coze环境');

      // 准备传递给Coze的数据
      const cozeData = this.prepareCozeData(data, type, domain);

      // 打开Coze环境并传递数据
      await this.openCozeEnvironment(cozeData);

      // 扣费（因为已经为用户提供了服务）
      if (this.billingManager) {
        try {
          await this.billingManager.executeBillingByFeature("exportToFeiShu", 1);
          console.log("✅ Coze环境已打开，扣费完成");
        } catch (billingError) {
          console.error("⚠️ Coze环境已打开但扣费失败:", billingError);
          // 服务已经提供了，扣费失败只记录日志
        }
      }

      showMessage("Coze环境已打开，请使用下方按钮复制数据到Coze表单", "success");

      // 显示数据复制操作界面
      this.showDataCopyInterface(cozeData, type);

    } catch (error) {
      console.error('❌ 飞书导出失败:', error);
      showMessage("导到多维表格失败: " + error.message, "error");
    } finally {
      // 清除防重复调用标志
      this._isExportingToFeiShu = false;
    }
  }

  // 准备传递给Coze的数据
  prepareCozeData(data, type, domain) {
    const timestamp = Date.now();

    return {
      // 基本信息
      title: `${this.getDataTypeDisplayName(type)}_${new Date().toLocaleString()}`,
      platform: domain,
      dataType: type,

      // 数据内容（保持原始对象，避免过度编码）
      data: data,

      // 统计信息
      count: Array.isArray(data) ? data.length : (data.items ? data.items.length : 1),
      fields: this.extractDataFields(data).join(', '),

      // 元数据
      timestamp: new Date().toISOString(),
      source: '多小宝浏览器插件',
      version: '1.0.0',

      // 会话信息
      sessionId: `plugin_${timestamp}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  // 提取数据字段信息
  extractDataFields(data) {
    if (Array.isArray(data) && data.length > 0) {
      return Object.keys(data[0]);
    } else if (data && typeof data === 'object') {
      if (data.items && Array.isArray(data.items) && data.items.length > 0) {
        return Object.keys(data.items[0]);
      }
      return Object.keys(data);
    }
    return [];
  }

  // 打开Coze环境并传递数据
  async openCozeEnvironment(cozeData) {
    try {
      console.log('🤖 打开Coze环境');

      // 从设置中获取Coze应用URL
      const cozeUrl = this.settings.cozeAppUrl;

      // 方式1: 通过URL参数传递数据（适合小数据）
      const dataJsonString = JSON.stringify(cozeData.data);
      if (dataJsonString.length < 2000) {
        const params = new URLSearchParams();
        params.set('plugin_title', cozeData.title);
        params.set('plugin_platform', cozeData.platform);
        params.set('plugin_dataType', cozeData.dataType);
        params.set('plugin_count', cozeData.count.toString());
        params.set('plugin_data', dataJsonString);
        params.set('plugin_source', 'browser_plugin');

        const fullUrl = `${cozeUrl}?${params.toString()}`;

        // 调试信息：显示传递给Coze的参数
        console.log('🔍 传递给Coze的URL参数:', {
          plugin_title: cozeData.title,
          plugin_platform: cozeData.platform,
          plugin_dataType: cozeData.dataType,
          plugin_count: cozeData.count,
          dataLength: dataJsonString.length,
          plugin_source: 'browser_plugin'
        });
        console.log('📄 实际传递的数据（前200字符）:', dataJsonString.substring(0, 200));
        console.log('🔗 完整URL:', fullUrl);

        // 在新标签页中打开Coze环境（侧栏模式优化）
        const cozeTab = await chrome.tabs.create({
          url: fullUrl,
          active: true
        });

        // 监听标签页状态，在完成后提供关闭选项
        this.setupCozeTabMonitor(cozeTab.id);

        console.log('📤 通过URL参数传递数据到Coze');
      }
      // 方式2: 通过LocalStorage传递数据（适合大数据）
      else {
        const storageKey = `coze_data_${cozeData.sessionId}`;
        localStorage.setItem(storageKey, JSON.stringify(cozeData));

        const params = new URLSearchParams();
        params.set('plugin_storage_key', storageKey);
        params.set('plugin_source', 'browser_plugin');

        const fullUrl = `${cozeUrl}?${params.toString()}`;

        // 在新标签页中打开Coze环境（侧栏模式优化）
        const cozeTab = await chrome.tabs.create({
          url: fullUrl,
          active: true
        });

        // 监听标签页状态，在完成后提供关闭选项
        this.setupCozeTabMonitor(cozeTab.id);

        console.log('💾 通过LocalStorage传递数据到Coze');

        // 10分钟后清理数据
        setTimeout(() => {
          localStorage.removeItem(storageKey);
          console.log('🗑️ 已清理临时数据:', storageKey);
        }, 10 * 60 * 1000);
      }

    } catch (error) {
      console.error('❌ 打开Coze环境失败:', error);
      throw error;
    }
  }

  // 监听Coze窗口，处理授权弹窗
  setupCozeWindowMonitor(cozeWindow) {
    if (!cozeWindow) return;

    console.log('🔍 开始监听Coze窗口');

    // 向Coze窗口注入脚本，拦截授权弹窗
    const injectScript = () => {
      try {
        // 检查窗口是否可访问（同域）
        if (cozeWindow.location.hostname) {
          // 注入脚本来拦截window.open调用
          const script = cozeWindow.document.createElement('script');
          script.textContent = `
            (function() {
              console.log('🔧 Coze窗口脚本已注入');

              // 保存原始的window.open方法
              const originalOpen = window.open;

              // 重写window.open方法
              window.open = function(url, target, features) {
                console.log('🔗 拦截到弹窗请求:', url);

                // 检查是否是飞书授权相关的URL
                if (url && (url.includes('feishu.cn') || url.includes('oauth') || url.includes('auth'))) {
                  console.log('✅ 检测到飞书授权弹窗，在当前窗口打开');
                  // 在当前窗口打开，而不是新窗口
                  window.location.href = url;
                  return window;
                } else {
                  // 其他弹窗使用原始方法
                  return originalOpen.call(this, url, target, features);
                }
              };

              // 监听页面中的链接点击
              document.addEventListener('click', function(e) {
                const link = e.target.closest('a');
                if (link && link.href && (link.href.includes('feishu.cn') || link.href.includes('oauth'))) {
                  console.log('🔗 拦截到飞书授权链接点击');
                  e.preventDefault();
                  window.location.href = link.href;
                }
              }, true);

              console.log('✅ 弹窗拦截脚本设置完成');
            })();
          `;
          cozeWindow.document.head.appendChild(script);
          console.log('✅ 已向Coze窗口注入弹窗拦截脚本');
        }
      } catch (error) {
        console.log('⚠️ 无法注入脚本（跨域限制）:', error.message);
        // 跨域情况下无法注入脚本，这是正常的
      }
    };

    // 等待窗口加载完成后注入脚本
    const checkAndInject = () => {
      if (cozeWindow.closed) {
        console.log('🔒 Coze窗口已关闭');
        return;
      }

      try {
        if (cozeWindow.document && cozeWindow.document.readyState === 'complete') {
          injectScript();
        } else {
          // 继续等待
          setTimeout(checkAndInject, 1000);
        }
      } catch (error) {
        // 跨域访问限制，继续尝试
        setTimeout(checkAndInject, 1000);
      }
    };

    // 开始检查和注入
    setTimeout(checkAndInject, 2000);
  }

  // 监听Coze标签页，提供完成后的操作选项
  setupCozeTabMonitor(tabId) {
    console.log('🔍 开始监听Coze标签页:', tabId);

    // 在侧栏中显示操作提示
    this.showCozeTabNotification(tabId);

    // 监听标签页更新，检测是否完成操作
    const updateListener = (updatedTabId, changeInfo, tab) => {
      if (updatedTabId === tabId && changeInfo.url) {
        console.log('📍 Coze标签页URL更新:', changeInfo.url);

        // 检测是否返回到成功页面或完成页面
        if (changeInfo.url.includes('success') || changeInfo.url.includes('complete')) {
          console.log('✅ 检测到操作完成');
          this.showCozeCompletionNotification(tabId);
        }
      }
    };

    // 监听标签页关闭
    const removeListener = (removedTabId) => {
      if (removedTabId === tabId) {
        console.log('🔒 Coze标签页已关闭');
        chrome.tabs.onUpdated.removeListener(updateListener);
        chrome.tabs.onRemoved.removeListener(removeListener);
        this.hideCozeNotifications();
      }
    };

    chrome.tabs.onUpdated.addListener(updateListener);
    chrome.tabs.onRemoved.addListener(removeListener);

    // 5分钟后自动清理监听器
    setTimeout(() => {
      chrome.tabs.onUpdated.removeListener(updateListener);
      chrome.tabs.onRemoved.removeListener(removeListener);
      console.log('🧹 Coze标签页监听器已清理');
    }, 5 * 60 * 1000);
  }

  // 显示Coze标签页操作通知
  showCozeTabNotification(tabId) {
    const notification = document.createElement('div');
    notification.id = 'coze-tab-notification';
    notification.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #4CAF50;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      font-size: 14px;
      max-width: 300px;
    `;

    notification.innerHTML = `
      <div style="margin-bottom: 8px;">
        <strong>🚀 Coze环境已打开</strong>
      </div>
      <div style="font-size: 12px; opacity: 0.9;">
        请在新标签页中完成数据导入操作
      </div>
      <button id="close-coze-tab" style="
        background: rgba(255,255,255,0.2);
        border: none;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        margin-top: 8px;
        cursor: pointer;
        font-size: 12px;
      ">关闭标签页</button>
    `;

    document.body.appendChild(notification);

    // 关闭标签页按钮
    document.getElementById('close-coze-tab').addEventListener('click', () => {
      chrome.tabs.remove(tabId);
      this.hideCozeNotifications();
    });

    // 10秒后自动隐藏
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 10000);
  }

  // 显示Coze完成通知
  showCozeCompletionNotification(tabId) {
    const notification = document.createElement('div');
    notification.id = 'coze-completion-notification';
    notification.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #2196F3;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      font-size: 14px;
      max-width: 300px;
    `;

    notification.innerHTML = `
      <div style="margin-bottom: 8px;">
        <strong>✅ 操作完成</strong>
      </div>
      <div style="font-size: 12px; opacity: 0.9;">
        数据导入已完成，可以关闭标签页了
      </div>
      <button id="close-completed-tab" style="
        background: rgba(255,255,255,0.2);
        border: none;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        margin-top: 8px;
        cursor: pointer;
        font-size: 12px;
      ">关闭标签页</button>
    `;

    document.body.appendChild(notification);

    // 关闭标签页按钮
    document.getElementById('close-completed-tab').addEventListener('click', () => {
      chrome.tabs.remove(tabId);
      this.hideCozeNotifications();
    });

    // 15秒后自动隐藏
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 15000);
  }

  // 隐藏所有Coze通知
  hideCozeNotifications() {
    const notifications = document.querySelectorAll('#coze-tab-notification, #coze-completion-notification');
    notifications.forEach(notification => {
      if (notification.parentNode) {
        notification.remove();
      }
    });
  }

  // 显示Coze操作指导
  showCozeInstructions() {
    // 创建指导弹窗
    const instructionModal = document.createElement('div');
    instructionModal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.7);
      z-index: 10000;
      display: flex;
      justify-content: center;
      align-items: center;
    `;

    const instructionContent = document.createElement('div');
    instructionContent.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 12px;
      max-width: 500px;
      width: 90%;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    instructionContent.innerHTML = `
      <h3 style="margin-top: 0; color: #333; text-align: center;">
        🤖 Coze环境操作指导
      </h3>

      <div style="margin: 20px 0; line-height: 1.6; color: #555;">
        <p><strong>📋 数据已传递到Coze，请按以下步骤操作：</strong></p>

        <ol style="padding-left: 20px;">
          <li style="margin-bottom: 10px;">
            <strong>在Coze窗口中</strong>找到飞书授权按钮并点击
          </li>
          <li style="margin-bottom: 10px;">
            <strong>如果弹出新窗口</strong>，请在新窗口中完成飞书授权
          </li>
          <li style="margin-bottom: 10px;">
            <strong>授权完成后</strong>，返回Coze窗口继续操作
          </li>
          <li style="margin-bottom: 10px;">
            <strong>确认数据导出</strong>到飞书多维表格
          </li>
        </ol>

        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-top: 15px;">
          <p style="margin: 0; font-size: 14px;">
            💡 <strong>提示：</strong>如果授权弹窗在新窗口打开，这是正常现象。
            完成授权后请返回Coze窗口继续操作。
          </p>
        </div>
      </div>

      <div style="text-align: center; margin-top: 25px;">
        <button id="closeInstructionBtn" style="
          background: #007bff;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
        ">我知道了</button>
      </div>
    `;

    instructionModal.appendChild(instructionContent);
    document.body.appendChild(instructionModal);

    // 关闭按钮事件
    const closeBtn = instructionContent.querySelector('#closeInstructionBtn');
    closeBtn.addEventListener('click', () => {
      document.body.removeChild(instructionModal);
    });

    // 点击背景关闭
    instructionModal.addEventListener('click', (e) => {
      if (e.target === instructionModal) {
        document.body.removeChild(instructionModal);
      }
    });

    // 5秒后自动关闭
    setTimeout(() => {
      if (document.body.contains(instructionModal)) {
        document.body.removeChild(instructionModal);
      }
    }, 8000);
  }

  // 显示数据复制操作界面
  showDataCopyInterface(cozeData, type = 'profile') {
    // 检查是否已经存在复制界面
    const existingInterface = document.getElementById('dataCopyInterface');
    if (existingInterface) {
      existingInterface.remove();
    }

    // 创建复制操作界面
    const copyInterface = document.createElement('div');
    copyInterface.id = 'dataCopyInterface';
    copyInterface.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: white;
      border: 2px solid #007bff;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.2);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 350px;
      min-width: 300px;
    `;

    // 根据导出类型选择对应的多维表格URL
    let tableUrl = '';
    if (type === 'profile') {
      // 个人主页数据使用个人主页表格URL
      tableUrl = this.settings.multidimensionalTableUrl || '';
    } else if (type === 'comment') {
      // 评论数据使用视频详情表格URL
      tableUrl = this.settings.videoDetailTableUrl || '';
    } else {
      // 默认使用个人主页表格URL
      tableUrl = this.settings.multidimensionalTableUrl || '';
    }

    copyInterface.innerHTML = `
      <div style="margin-bottom: 15px;">
        <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">
          📋 数据复制助手
        </h4>
        <p style="margin: 0; color: #666; font-size: 12px; line-height: 1.4;">
          点击按钮复制数据，然后粘贴到Coze表单中
        </p>
      </div>

      <div style="display: flex; flex-direction: column; gap: 10px;">
        <!-- 复制多维表格URL按钮 -->
        <button id="copyTableUrlBtn" style="
          background: #17a2b8;
          color: white;
          border: none;
          padding: 12px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
        ">
          🔗 复制${type === 'profile' ? '个人主页' : '视频详情'}表格链接
        </button>

        <!-- 复制完整数据对象按钮 -->
        <button id="copyFullDataBtn" style="
          background: #fd7e14;
          color: white;
          border: none;
          padding: 12px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
        ">
          📦 复制完整数据
        </button>
      </div>

      <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <small style="color: #666; font-size: 11px;">
            数据条数: ${cozeData.count}
          </small>
          <button id="closeCopyInterface" style="
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
          ">
            关闭
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(copyInterface);

    // 绑定按钮事件
    this.bindCopyInterfaceEvents(copyInterface, cozeData, tableUrl, type);

    // 10分钟后自动关闭
    setTimeout(() => {
      if (document.body.contains(copyInterface)) {
        copyInterface.remove();
      }
    }, 10 * 60 * 1000);
  }


  // 绑定复制界面事件
  bindCopyInterfaceEvents(copyInterface, cozeData, tableUrl, type = 'profile') {
    // 复制多维表格URL
    const copyTableUrlBtn = copyInterface.querySelector('#copyTableUrlBtn');
    copyTableUrlBtn.addEventListener('click', async () => {
      try {
        if (!tableUrl || tableUrl.trim() === '') {
          const tableType = type === 'profile' ? '个人主页' : '视频详情';
          this.showCopyError(copyTableUrlBtn, `⚠️ 请先设置${tableType}表格链接`);
          console.log(`⚠️ ${tableType}多维表格URL未设置`);
          return;
        }

        await navigator.clipboard.writeText(tableUrl);
        this.showCopySuccess(copyTableUrlBtn, '✅ 链接已复制');
        console.log('📋 表格链接已复制到剪贴板');
      } catch (error) {
        console.error('复制失败:', error);
        this.showCopyError(copyTableUrlBtn, '❌ 复制失败');
      }
    });

    // 复制完整数据对象
    const copyFullDataBtn = copyInterface.querySelector('#copyFullDataBtn');
    copyFullDataBtn.addEventListener('click', async () => {
      try {
        // 复制原生JSON纯文本，不添加任何格式化
        const dataContent = typeof cozeData.data === 'string' ? JSON.parse(cozeData.data) : cozeData.data;

        const cleanCozeData = {
          ...cozeData,
          data: dataContent
        };

        // 直接转换为JSON字符串，不进行格式化
        const dataString = JSON.stringify(cleanCozeData);

        await navigator.clipboard.writeText(dataString);
        this.showCopySuccess(copyFullDataBtn, '✅ 完整数据已复制');
        console.log('📋 完整数据（JSON纯文本）已复制到剪贴板');
      } catch (error) {
        console.error('复制失败:', error);
        this.showCopyError(copyFullDataBtn, '❌ 复制失败');
      }
    });

    // 关闭按钮
    const closeBtn = copyInterface.querySelector('#closeCopyInterface');
    closeBtn.addEventListener('click', () => {
      copyInterface.remove();
    });
  }

  // 显示复制成功提示
  showCopySuccess(button, message) {
    const originalText = button.innerHTML;
    const originalBackground = this.getButtonOriginalColor(button.id);

    button.innerHTML = message;
    button.style.background = '#28a745';

    setTimeout(() => {
      button.innerHTML = originalText;
      button.style.background = originalBackground;
    }, 2000);
  }

  // 显示复制失败提示
  showCopyError(button, message) {
    const originalText = button.innerHTML;
    const originalBackground = this.getButtonOriginalColor(button.id);

    button.innerHTML = message;
    button.style.background = '#dc3545';

    setTimeout(() => {
      button.innerHTML = originalText;
      button.style.background = originalBackground;
    }, 2000);
  }

  // 获取按钮原始颜色
  getButtonOriginalColor(buttonId) {
    const colorMap = {
      'copyTableUrlBtn': '#17a2b8',
      'copyFullDataBtn': '#fd7e14'
    };
    return colorMap[buttonId] || '#007bff';
  }



  // 获取数据类型显示名称
  getDataTypeDisplayName(type) {
    const typeMap = {
      'comment': '评论数据',
      'user_profile': '用户资料',
      'profile': '个人主页视频',
      'video': '视频数据',
      'live': '直播数据'
    };
    return typeMap[type] || type;
  }

  // 调用后端API代理
  async callCozeAPI(tableUrl, domain, type, data) {
    try {
      console.log("🚀 开始调用后端API代理接口");
      
      // 获取API Token
      const apiToken = await this.getApiToken();
      if (!apiToken) {
        throw new Error("未找到API Token，请先登录");
      }

      const requestBody = {
        parameters: {
          url: tableUrl,
          domain: domain,
          type: type,
          data: JSON.stringify(data),
        },
        workflow_id: "7533062669940391978",
      };

      const feishuExportUrl = window.pluginConfig?.getProxyEndpoint('/feishu_export')
      const response = await fetch(feishuExportUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiToken  // 添加API Key
        },
        body: JSON.stringify(requestBody),
      });
      
      console.log("� 请求参数:", { tableUrl, domain, type, data });

      console.log("📤 发送到后端的数据:", requestBody);

      console.log("�📡 收到后端响应:", response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ 后端响应错误:", errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }

      const result = await response.json();
      console.log("📥 后端返回结果:", result);

      // 检查后端返回的结果格式
      if (result.code === 200) {
        console.log("✅ 飞书导出成功!");
        showMessage("数据已成功导到多维表格", "success");
        return result.data;
      } else {
        console.error("❌ 后端返回错误:", result);
        throw new Error(result.msg || "API调用失败");
      }
    } catch (error) {
      console.error("💥 飞书导出异常:", error);
      throw new Error(`飞书导出失败: ${error.message}`);
    }
  }

  // 获取API Token的辅助方法
  async getApiToken() {
    try {
      const result = await chrome.storage.local.get("api_token");
      return result.api_token || null;
    } catch (error) {
      console.error("获取API令牌失败:", error);
      return null;
    }
  }

  // 显示错误
  showError(message) {
    const extractBtn = document.getElementById("extractBtn");

    // 移除对已注释页面状态元素的引用

    if (extractBtn) {
      extractBtn.innerHTML = '<span class="btn-icon">❌</span>无法获取';
      extractBtn.style.background = "#6b7280";
      extractBtn.disabled = true;
      // 设置默认的dataset属性，避免undefined错误
      extractBtn.dataset.pageType = "unknown";
      extractBtn.dataset.action = "none";
    }

    this.updateStatus("error", message);
  }

  // 处理常规获取完成
  handleExtractionComplete(data) {
    // 更新数据管理器
    this.dataManager.updateData(data);
    // 更新所有显示
    this.updateAllDisplays();
    // 显示成功消息，但不立即重置按钮状态，等待EXTRACTION_FINISHED消息
  }
}

// 辅助函数
function showMessage(message, type = "info") {
  // 移除已存在的通知
  const existingNotifications = document.querySelectorAll('.custom-notification');
  existingNotifications.forEach(notification => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  });

  const notification = document.createElement("div");
  notification.className = 'custom-notification';
  notification.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 20px 24px;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    z-index: 99999;
    max-width: 400px;
    min-width: 300px;
    text-align: center;
    word-wrap: break-word;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.2);
    animation: slideIn 0.3s ease-out;
  `;

  // 根据类型设置不同的背景色和边框
  switch (type) {
    case "success":
      notification.style.background = "linear-gradient(135deg, #4CAF50, #45a049)";
      notification.style.borderColor = "#4CAF50";
      break;
    case "error":
      notification.style.background = "linear-gradient(135deg, #f44336, #d32f2f)";
      notification.style.borderColor = "#f44336";
      break;
    case "warning":
      notification.style.background = "linear-gradient(135deg, #ff9800, #f57c00)";
      notification.style.borderColor = "#ff9800";
      break;
    case "info":
    default:
      notification.style.background = "linear-gradient(135deg, #2196F3, #1976d2)";
      notification.style.borderColor = "#2196F3";
      break;
  }

  // 添加动画样式
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
      }
      to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
    }
    @keyframes slideOut {
      from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
      to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
      }
    }
  `;
  if (!document.querySelector('#notification-styles')) {
    style.id = 'notification-styles';
    document.head.appendChild(style);
  }

  notification.textContent = message;
  document.body.appendChild(notification);

  // 4秒后自动移除
  setTimeout(() => {
    if (document.body.contains(notification)) {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }
  }, 4000);
}

// 全局变量
let popupManager;

// 初始化
document.addEventListener("DOMContentLoaded", () => {
  popupManager = new PopupManager();
});

// 数据管理器
class DataManager {
  constructor() {
    this.listVideos = []; // 列表视频数据
    this.detailVideos = []; // 详情视频数据
    this.comments = []; // 评论数据
    this.userProfiles = []; // 用户资料数据
  }

  // 加载所有数据
  async loadAllData() {
    try {
      const result = await chrome.storage.local.get([
        "douyinData",
        "douyinVideoDetails",
      ]);

      // 加载列表数据 - 保存完整的session数据
      const listData = result.douyinData || [];
      this.userProfiles = listData; // 保存完整的session数据
      this.listVideos = [];
      const seenVideoIds = new Set(); // 用于去重

      listData.forEach((session) => {
        if (session.videos) {
          session.videos.forEach((video) => {
            // 只添加未见过的视频ID
            if (video.id && !seenVideoIds.has(video.id)) {
              seenVideoIds.add(video.id);
              this.listVideos.push(video);
            } else if (!video.id) {
              // 如果没有ID，生成一个基于内容的唯一标识
              const contentKey =
                `${video.title}_${video.author}_${video.coverUrl}`.substring(
                  0,
                  100
                );
              if (!seenVideoIds.has(contentKey)) {
                seenVideoIds.add(contentKey);
                this.listVideos.push(video);
              }
            }
          });
        }
        if (session.comments) {
          this.comments.push(...session.comments);
        }
      });

      // 加载详情数据
      this.detailVideos = result.douyinVideoDetails || [];
    } catch (error) {
      // 加载数据失败，但不影响主要功能
    }
  }

  // 更新数据
  updateData(newData) {
    if (newData.videos) {
      this.listVideos.push(...newData.videos);
    }
    if (newData.comments) {
      this.comments.push(...newData.comments);
    }
    // 如果是用户profile数据，添加到userProfiles
    if (newData.userInfo) {
      // 检查是否已存在相同用户
      const userKey =
        newData.userInfo?.douyinId || newData.userInfo?.name || newData.url;
      const existingIndex = this.userProfiles.findIndex((profile) => {
        const profileUserKey =
          profile.userInfo?.douyinId || profile.userInfo?.name || profile.url;
        return profileUserKey === userKey;
      });

      if (existingIndex !== -1) {
        // 如果已存在，替换旧数据
        this.userProfiles[existingIndex] = newData;
      } else {
        // 如果不存在，添加新数据
        this.userProfiles.push(newData);
      }
    }
  }

  // 获取最新用户信息
  getLatestUserInfo() {
    if (this.userProfiles.length === 0) return null;
    return this.userProfiles[this.userProfiles.length - 1];
  }

  // 获取个人主页视频 - 返回最新用户的视频
  getProfileVideos() {
    const latestUser = this.getLatestUserInfo();
    return latestUser?.videos || [];
  }

  // 获取用户资料列表
  getUserProfiles() {
    return this.userProfiles;
  }

  // 获取详情视频
  getDetailVideos() {
    return this.detailVideos;
  }

  // 获取所有评论
  getAllComments() {
    const allComments = [];

    // 添加详情视频中的评论
    this.detailVideos.forEach((video) => {
      if (video.comments && Array.isArray(video.comments)) {
        // 为每条评论添加视频信息
        const commentsWithVideoInfo = video.comments.map((comment) => ({
          ...comment,
          videoId: video.videoId,
          videoTitle: video.videoInfo?.title || "未知视频",
          videoAuthor: video.videoInfo?.author?.name || "未知作者",
          extractedAt:
            video.extractedAt ||
            comment.extractedAt ||
            new Date().toISOString(),
        }));
        allComments.push(...commentsWithVideoInfo);
      }
    });

    // 添加独立评论
    this.comments.forEach((comment) => {
      if (!comment.videoId) {
        allComments.push(comment);
      }
    });

    return allComments;
  }

  // 获取指定分类的数据
  getCategoryData(category) {
    switch (category) {
      case "profile":
        return this.listVideos;
      case "detail":
        return this.detailVideos;
      case "comment":
        return this.getAllComments();
      default:
        return [];
    }
  }

  // 清空指定分类的数据
  async clearCategoryData(category) {
    switch (category) {
      case "profile":
        this.listVideos = [];
        this.userProfiles = []; // 也要清空用户资料数据
        await chrome.storage.local.set({ douyinData: [] });
        break;
      case "detail":
        this.detailVideos = [];
        await chrome.storage.local.set({ douyinVideoDetails: [] });
        break;
      case "comment":
        this.comments = [];
        // 清空详情视频中的评论
        this.detailVideos.forEach((video) => {
          if (video.comments) {
            video.comments = [];
          }
        });
        // 保存更新后的详情视频数据
        await chrome.storage.local.set({ douyinVideoDetails: this.detailVideos });
        break;
    }
  }

  // 检查是否有数据
  hasData() {
    return (
      this.listVideos.length > 0 ||
      this.detailVideos.length > 0 ||
      this.comments.length > 0
    );
  }

  // 重置所有数据
  resetData() {
    this.listVideos = [];
    this.detailVideos = [];
    this.comments = [];
  }

  // 获取所有数据
  getData() {
    return {
      videos: this.listVideos,
      detailVideos: this.detailVideos,
      comments: this.getAllComments(),
      extractedAt: new Date().toISOString(),
    };
  }

  // 添加详情视频
  addDetailVideo(videoData) {
    // 检查是否已存在相同ID的视频
    const existingIndex = this.detailVideos.findIndex(
      (video) => video.videoId === videoData.videoId
    );

    if (existingIndex !== -1) {
      // 如果已存在，替换旧数据
      this.detailVideos[existingIndex] = videoData;
    } else {
      // 如果不存在，添加新数据
      this.detailVideos.push(videoData);
    }
  }
}

// 分类导出函数
async function exportCategoryData(category, format) {
  const dataManager = popupManager.dataManager;

  if (category === "profile") {
    // 获取最新的存储数据
    chrome.storage.local.get(["douyinData"], async (result) => {
      const storedData = result.douyinData || [];
      if (storedData.length === 0) {
        showMessage("暂无数据可导出", "warning");
        return;
      }
      const timestamp = new Date().toISOString().split("T")[0];
      let content, filename, mimeType;
      if (format === "json") {
        // JSON导出使用结构化数据
        const latestData = storedData[storedData.length - 1];
        content = JSON.stringify(
          latestData.structuredData || latestData,
          null,
          2
        );
        filename = `douyin-structured-${timestamp}.json`;
        mimeType = "application/json";
      } else if (format === "csv") {
        // CSV导出简化版本，不包含用户详细信息
        content = convertVideosToSimpleCSV(storedData);
        filename = `douyin-videos-${timestamp}.csv`;
        mimeType = "text/csv;charset=utf-8;";
      } else if (format === "feishu") {
        const latestData = storedData[storedData.length - 1];
        // 传递对象而不是字符串给exportToFeiShu
        let data = latestData.structuredData || latestData;
        popupManager.exportToFeiShu("douyin", "profile", data);
        return; // 飞书导出不需要下载文件
      }

      // 下载文件（JSON和CSV格式）
      if (content && filename && mimeType) {
        const downloadSuccess = await downloadFile(content, filename, mimeType);
        if (downloadSuccess) {
          showMessage(`数据导出成功`, "success");
        }
      } else {
        showMessage(`数据导出成功`, "success");
      }
    });
  } else if (category === "comment") {
    // 评论数据特殊处理，包含完整视频信息
    const detailVideos = dataManager.getDetailVideos();

    if (detailVideos.length === 0) {
      showMessage("暂无评论数据可导出", "warning");
      return;
    }

    const timestamp = new Date().toISOString().split("T")[0];
    let content, filename, mimeType;

    // 构建包含视频基本信息的评论数据结构 - 提取到外部，避免重复定义
    const commentDataWithVideoInfo = detailVideos
      .filter((video) => video.comments && video.comments.length > 0)
      .map((video) => ({
        videoId: video.videoId || extractVideoIdFromUrl(video.url),
        title: video.videoInfo?.title || "未知标题",
        author: video.videoInfo?.author?.name || "未知作者",
        authorAvatar: video.videoInfo?.author?.avatar || "",
        authorID:
          video.videoInfo?.author?.uid || video.videoInfo?.author?.id || "",
        authorLink: "",
        cover: video.videoInfo?.cover || "",
        duration: video.videoInfo?.duration || "",
        publishTime: video.videoInfo?.publishTime || "",
        link: video.url || "",
        likes: video.videoInfo?.stats?.likes || 0,
        shares: video.videoInfo?.stats?.shares || 0,
        collects: video.videoInfo?.stats?.collections || 0,
        commentCount: video.comments.length,
        isTop: false,
        extractedAt: video.extractedAt || new Date().toISOString(),
        comments: video.comments.map((comment) => ({
          userName: comment.userName || "匿名用户",
          avatar: comment.avatar || "",
          content: comment.content || "",
          likes: comment.likes || 0,
          date: comment.date || "",
          location: comment.location || "",
          isAuthor: comment.isAuthor || false,
          extractedAt: comment.extractedAt || new Date().toISOString(),
        })),
      }));

    // 创建通用的JSON数据结构函数（返回字符串，用于文件导出）
    const createCommentJsonData = () => {
      if (commentDataWithVideoInfo.length === 1) {
        // 单个视频的评论数据
        return JSON.stringify(
          {
            domain: "douyin",
            type: "comment",
            data: {
              ...commentDataWithVideoInfo[0],
            },
            exportTime: new Date().toISOString(),
          },
          null,
          2
        );
      } else {
        // 多个视频的评论数据
        return JSON.stringify(
          {
            domain: "douyin",
            type: "comment",
            exportTime: new Date().toISOString(),
            totalVideos: commentDataWithVideoInfo.length,
            totalComments: commentDataWithVideoInfo.reduce(
              (sum, video) => sum + video.comments.length,
              0
            ),
            data: commentDataWithVideoInfo,
          },
          null,
          2
        );
      }
    };

    // 创建对象格式的数据结构函数（返回对象，用于飞书导出）
    const createCommentObjectData = () => {
      if (commentDataWithVideoInfo.length === 1) {
        // 单个视频的评论数据
        return {
          domain: "douyin",
          type: "comment",
          data: {
            ...commentDataWithVideoInfo[0],
          },
          exportTime: new Date().toISOString(),
        };
      } else {
        // 多个视频的评论数据
        return {
          domain: "douyin",
          type: "comment",
          exportTime: new Date().toISOString(),
          totalVideos: commentDataWithVideoInfo.length,
          totalComments: commentDataWithVideoInfo.reduce(
            (sum, video) => sum + video.comments.length,
            0
          ),
          data: commentDataWithVideoInfo,
        };
      }
    };

    if (format === "json") {
      content = createCommentJsonData();
      filename = `douyin-comments-${timestamp}.json`;
      mimeType = "application/json";
    } else if (format === "csv") {
      content = convertCategoryToCSV(category, dataManager.getAllComments());
      filename = `douyin-${category}-${timestamp}.csv`;
      mimeType = "text/csv;charset=utf-8;";
    } else if (format === "feishu") {
      // 使用对象格式的数据创建函数
      const feishuContent = createCommentObjectData();
      popupManager.exportToFeiShu("douyin", "comment", feishuContent);
      return; // 飞书导出不需要下载文件
    }
    const downloadSuccess = await downloadFile(content, filename, mimeType);
    if (downloadSuccess) {
      showMessage(`评论数据导出成功`, "success");
    }
  } else {
    // 其他分类保持原有逻辑
    const data = dataManager.getCategoryData(category);

    if (data.length === 0) {
      showMessage("该分类暂无数据可导出", "warning");
      return;
    }

    const timestamp = new Date().toISOString().split("T")[0];
    let content, filename, mimeType;

    if (format === "json") {
      content = JSON.stringify(
        {
          category: category,
          data: data,
          count: data.length,
          exportTime: new Date().toISOString(),
        },
        null,
        2
      );
      filename = `douyin-${category}-${timestamp}.json`;
      mimeType = "application/json";
    } else if (format === "csv") {
      content = convertCategoryToCSV(category, data);
      filename = `douyin-${category}-${timestamp}.csv`;
      mimeType = "text/csv;charset=utf-8;";
    }

    // 统一处理文件下载，只有成功时才显示成功消息
    const downloadSuccess = await downloadFile(content, filename, mimeType);
    if (downloadSuccess) {
      showMessage(`${category}数据导出成功`, "success");
    }
  }
}

// 转换视频数据为简化CSV格式
function convertVideosToSimpleCSV(storedData) {
  let csv = "\uFEFF"; // BOM头

  // CSV表头 - 简化版本，不包含用户详细信息
  csv += "ID,标题,点赞数,是否置顶,视频链接,封面链接,获取时间\n";

  storedData.forEach((session) => {
    if (session.videos && session.videos.length > 0) {
      session.videos.forEach((video) => {
        const id = escapeCsvField(video.id || "");
        const title = escapeCsvField(video.title || "");
        const likes = escapeCsvField(video.likes || "0");
        const isTop = video.isTop ? "是" : "否";
        const videoUrl = escapeCsvField(video.videoUrl || "");
        const coverUrl = escapeCsvField(video.coverUrl || "");
        const extractedAt = escapeCsvField(video.extractedAt || "");

        csv += `${id},${title},${likes},${isTop},${videoUrl},${coverUrl},${extractedAt}\n`;
      });
    }
  });

  return csv;
}

// CSV字段转义处理
function escapeCsvField(field) {
  if (field === null || field === undefined) {
    return "";
  }

  // 转换为字符串
  let str = String(field);

  // 如果包含逗号、引号、换行符，需要用引号包围并转义内部引号
  if (
    str.includes(",") ||
    str.includes('"') ||
    str.includes("\n") ||
    str.includes("\r")
  ) {
    str = '"' + str.replace(/"/g, '""') + '"';
  }

  return str;
}

// 清空分类数据
async function clearCategoryData(category) {
  const categoryNames = {
    profile: "个人主页视频",
    detail: "详情视频",
    comment: "评论",
  };

  if (confirm(`确定要清空${categoryNames[category]}数据吗？`)) {
    await popupManager.dataManager.clearCategoryData(category);
    popupManager.updateAllDisplays();
    showMessage(`${categoryNames[category]}数据已清空`, "success");
  }
}

// 清空所有数据
async function clearAllData() {
  if (confirm("确定要清空所有数据吗？此操作不可恢复！")) {
    await popupManager.dataManager.clearCategoryData("profile");
    await popupManager.dataManager.clearCategoryData("detail");
    await popupManager.dataManager.clearCategoryData("comment");
    popupManager.updateAllDisplays();
    showMessage("所有数据已清空", "success");
  }
}

// 转换分类数据为CSV
function convertCategoryToCSV(category, data) {
  let csv = "\uFEFF"; // BOM头

  switch (category) {
    case "profile":
      csv += "ID,标题,作者,时长,点赞数,发布时间,获取时间\n";
      data.forEach((video) => {
        csv += `"${video.id}","${video.title}","${video.author}","${video.duration}","${video.likes}","${video.publishTime}","${video.extractedAt}"\n`;
      });
      break;
    case "detail":
      csv += "ID,标题,作者,点赞数,评论数,分享数,收藏数,发布时间,获取时间\n";
      data.forEach((video) => {
        csv += `"${video.videoId}","${video.title}","${video.author.name}","${video.interactions.likes}","${video.interactions.comments}","${video.interactions.shares}","${video.interactions.collects}","${video.publishTime}","${video.extractedAt}"\n`;
      });
      break;
    case "comment":
      csv += "作者,内容,点赞数,视频ID,获取时间\n";
      data.forEach((comment) => {
        csv += `"${comment.author}","${comment.content}","${comment.likes}","${
          comment.videoId || ""
        }","${comment.extractedAt}"\n`;
      });
      break;
  }

  return csv;
}

// 下载文件（带扣费功能）
async function downloadFile(content, filename, mimeType, skipBilling = false) {
  try {
    // 如果不跳过扣费，则执行扣费逻辑
    if (!skipBilling) {
      // 根据文件类型确定功能名称
      let featureName = "json_export"; // 默认
      if (filename.includes(".csv")) {
        featureName = "csv_export";
      } else if (filename.includes(".xlsx") || filename.includes(".xls")) {
        featureName = "excel_export";
      }

      // 使用新的权限管理架构
      if (window.permissionGuard) {
        const permissionResult = await window.permissionGuard.checkPermissionAndBilling(
          featureName,
          'douyin'
        );

        if (!permissionResult.success) {
          console.error(`${featureName}权限检查失败:`, permissionResult.error);
          return false;
        }
      }
      // 兼容旧的扣费管理器
      else if (window.billingManager) {
        const canProceed = await window.billingManager.showBillingPrompt(
          featureName
        );
        if (!canProceed) {
          return false; // 返回失败状态
        }

        // 先检查余额，不立即扣费
        try {
          const productId = window.billingManager.getProductId();
          const balanceCheck = await window.billingManager.checkBalance(productId, 1);
          if (!balanceCheck.sufficient) {
            showMessage(`点数不足，无法下载。当前余额：${balanceCheck.currentBalance}，需要：${balanceCheck.requiredPoints}`, "error");
            return false;
          }
          console.log(`${featureName}余额检查通过，准备下载`);
        } catch (error) {
          console.error(`${featureName}余额检查失败:`, error);
          showMessage("余额检查失败: " + error.message, "error");
          return false;
        }
      }
    }

    // 执行下载
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);

    return new Promise(async (resolve) => {
      chrome.downloads.download(
        {
          url: url,
          filename: filename,
          saveAs: true,
        },
        async (downloadId) => {
          URL.revokeObjectURL(url);
          if (chrome.runtime.lastError) {
            console.error("下载失败:", chrome.runtime.lastError);
            showMessage("文件下载失败", "error");
            resolve(false);
          } else {
            // 下载成功后才扣费（如果需要扣费）
            if (!skipBilling && window.billingManager) {
              try {
                // 根据文件类型确定功能名称
                let featureName = "json_export"; // 默认
                if (filename.includes(".csv")) {
                  featureName = "csv_export";
                } else if (filename.includes(".xlsx") || filename.includes(".xls")) {
                  featureName = "excel_export";
                }

                await window.billingManager.executeBillingByFeature(featureName, 1);
                console.log(`✅ ${featureName}下载成功，扣费完成`);
              } catch (billingError) {
                console.error(`⚠️ 下载成功但扣费失败:`, billingError);
                // 下载已经成功了，扣费失败只记录日志，不影响用户体验
              }
            }
            resolve(true); // 下载成功
          }
        }
      );
    });
  } catch (error) {
    console.error("下载文件失败:", error);
    showMessage("下载失败: " + error.message, "error");
    return false;
  }
}

// 从URL中提取视频ID的辅助函数
function extractVideoIdFromUrl(url) {
  if (!url) return "";

  // 匹配抖音视频URL中的ID
  const patterns = [
    /\/video\/(\d+)/, // 标准格式: /video/*********
    /\/share\/video\/(\d+)/, // 分享格式: /share/video/*********
    /aweme_id=(\d+)/, // 参数格式: ?aweme_id=*********
    /modal_id=(\d+)/, // 模态框格式: ?modal_id=*********
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return "";
}

// 扣费管理方法
PopupManager.prototype.setupBillingManagement = function () {
  // 设置API令牌管理
  this.setupApiTokenManagement();

  // 设置账户管理
  this.setupAccountManagement();

  // 初始化扣费管理器
  this.initializeBilling();
};

PopupManager.prototype.setupApiTokenManagement = function () {
  // 页面加载时自动读取已保存的API令牌
  this.loadSavedApiToken();

  // 设置令牌显示/隐藏切换功能
  this.setupTokenVisibilityToggle();

  // 保存API令牌
  const saveTokenBtn = document.getElementById("saveTokenBtn");
  if (saveTokenBtn) {
    saveTokenBtn.addEventListener("click", async () => {
      const token = document.getElementById("apiTokenInput").value.trim();
      if (!token) {
        alert("请输入API令牌");
        return;
      }

      try {
        const success = await this.billingManager.setApiToken(token);
        if (success) {
          alert("API令牌保存成功");
          // 自动更新账户信息和API状态
          await this.updateAccountDisplay();
          await this.updateApiStatus();
        } else {
          alert("API令牌无效");
        }
      } catch (error) {
        alert("保存API令牌失败: " + error.message);
      }
    });
  }

  // 清除API令牌
  const clearTokenBtn = document.getElementById("clearTokenBtn");
  if (clearTokenBtn) {
    clearTokenBtn.addEventListener("click", async () => {
      if (confirm("确认清除API令牌？")) {
        await this.billingManager.clearApiToken();
        document.getElementById("apiTokenInput").value = "";
        await this.updateAccountDisplay();
        await this.updateApiStatus();
        alert("API令牌已清除");
      }
    });
  }
};

// 加载已保存的API令牌
PopupManager.prototype.loadSavedApiToken = async function () {
  try {
    const apiToken = await this.billingManager.getApiToken();
    const tokenInput = document.getElementById("apiTokenInput");
    if (tokenInput && apiToken) {
      tokenInput.value = apiToken;
    }
  } catch (error) {
    console.error("加载API令牌失败:", error);
  }
};

// 设置令牌显示/隐藏切换功能
PopupManager.prototype.setupTokenVisibilityToggle = function () {
  const toggleBtn = document.getElementById("toggleTokenVisibility");
  const tokenInput = document.getElementById("apiTokenInput");
  
  if (toggleBtn && tokenInput) {
    toggleBtn.addEventListener("click", () => {
      if (tokenInput.type === "password") {
        tokenInput.type = "text";
        toggleBtn.textContent = "🙈";
        toggleBtn.title = "隐藏令牌";
      } else {
        tokenInput.type = "password";
        toggleBtn.textContent = "👁️";
        toggleBtn.title = "显示令牌";
      }
    });
  }
};

PopupManager.prototype.setupAccountManagement = function () {
  // 移除刷新按钮相关代码，账户信息将自动更新
  // 页面初始化时自动加载账户信息
  this.updateAccountDisplay();
};

PopupManager.prototype.initializeBilling = async function () {
  try {
    // 设置服务器地址
    if (this.settings.serverUrl) {
      this.billingManager.setServerUrl(
        this.settings.serverUrl + "/api/browser-extension"
      );
    }

    // 初始化扣费管理器
    await this.billingManager.initialize();

    console.log("扣费管理器初始化成功");
  } catch (error) {
    console.error("扣费管理器初始化失败:", error);
  }
};

PopupManager.prototype.updateAccountDisplay = async function () {
  try {
    const apiToken = await this.billingManager.getApiToken();

    if (!apiToken) {
      this.showNotLoggedIn();
      return;
    }

    const memberInfo = await this.billingManager.getMemberInfo();

    // 更新账户信息
    const accountUsername = document.getElementById("accountUsername");
    const accountBalance = document.getElementById("accountBalance");

    if (accountUsername)
      accountUsername.textContent = memberInfo.username || "-";
    if (accountBalance) accountBalance.textContent = `${memberInfo.points} 点`;

    // 更新API状态
    await this.updateApiStatus(true);
  } catch (error) {
    console.error("更新账户显示失败:", error);
    this.showNotLoggedIn();
    await this.updateApiStatus(false);
  }
};

PopupManager.prototype.showNotLoggedIn = function () {
  const accountUsername = document.getElementById("accountUsername");
  const accountBalance = document.getElementById("accountBalance");

  if (accountUsername) accountUsername.textContent = "未登录";
  if (accountBalance) accountBalance.textContent = "-- 点";
};

PopupManager.prototype.updateApiStatus = async function (connected = null) {
  const indicator = document.getElementById("apiStatusIndicator");
  const text = document.getElementById("apiStatusText");

  if (!indicator || !text) return;

  if (connected === null) {
    // 检测连接状态
    try {
      const apiToken = await this.billingManager.getApiToken();
      if (!apiToken) {
        connected = false;
      } else {
        // 通过获取会员信息来检测连接状态
        await this.billingManager.getMemberInfo();
        connected = true;
      }
    } catch (error) {
      connected = false;
    }
  }

  // 移除之前的状态类
  indicator.classList.remove('connected', 'disconnected');
  
  if (connected) {
    indicator.classList.add('connected');
    text.textContent = "API连接正常";
  } else {
    indicator.classList.add('disconnected');
    text.textContent = "API连接失败";
  }
};
