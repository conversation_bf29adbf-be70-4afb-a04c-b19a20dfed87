// 巨量引擎榜单数据获取器 - Content Script


/**
 * 巨量引擎榜单数据获取器
 * 专门用于获取巨量引擎平台的各类榜单数据
 */
class OceanEngineRankingExtractor {
  constructor() {
    // 初始化获取状态
    this.isExtracting = false;
    this.maxExtractCount = 100;
    this.extractCount = 0;
    this.currentPage = 1;

    // 初始化数据存储结构
    this.extractedData = {
      hot_content: {
        short_video: [],      // 短视频榜
        picture_content: [],  // 图文榜
        product_card: []      // 商品卡榜
      },
      aweme: {
        Retailers: [],        // 电商行业榜
        App: [],              // 应用下载榜
        Clue: [],             // 线索收集榜
        ShortVideo: []        // 短视频主题榜
      },
      ranking: {
        hot_word: [],         // 热词榜
        hot_topic: [],        // 热点榜
        marketing_hot_pots: [] // 营销热点榜
      }
    };

    // 初始化消息监听
    this.setupMessageListener();

    // 添加提取按钮
    this.init();
  }

  // 初始化
  init() {
    this.detectPageType();
  }

  // 检测页面类型
  detectPageType() {
    try {
      // 获取当前URL
      const currentUrl = window.location.href;

      // 首先检查是否在巨量引擎域名下
      if (!currentUrl.includes('oceanengine.com')) {
        return { isRankingPage: false, type: 'unknown', info: '非巨量引擎平台页面' };
      }


      // 基于URL路径识别榜单类型
      const rankingType = this.identifyRankingTypeByUrl(currentUrl);

      if (rankingType) {
        return {
          isRankingPage: true,
          type: 'ranking',
          rankingType: rankingType,
          info: `${rankingType.categoryName} - ${rankingType.typeName}`
        };
      }

      // 如果URL没有匹配到，尝试通过页面元素检测


      // 检测榜单相关元素
      const hasRankingElements = document.querySelector('.yuntu_st_main-menu-item') ||
        document.querySelector('[data-log-value]') ||
        document.querySelector('.rank-list-table-body');

      if (hasRankingElements) {
        return {
          isRankingPage: true,
          type: 'ranking',
          rankingType: this.identifyRankingTypeByContent(),
          info: '榜单页面 (通过页面元素检测)'
        };
      }

      // 未识别为榜单页面
      return {
        isRankingPage: false,
        type: 'normal',
        info: '普通巨量引擎页面'
      };
    } catch (error) {
      return {
        isRankingPage: false,
        type: 'error',
        info: '页面检测出错: ' + error.message
      };
    }
  }

  // 基于URL识别榜单类型
  identifyRankingTypeByUrl(url) {



    const normalizedUrl = url.toLowerCase();

    // 提取URL参数进行更精确的判断
    const urlParams = new URLSearchParams(new URL(url).search);
    const rankType = urlParams.get('rank_type') || urlParams.get('type');
    const category = urlParams.get('category');






    try {


      // 规范化URL，转换为小写便于匹配
      const normalizedUrl = url.toLowerCase();

      // 解析URL参数，有些榜单类型可能在参数中
      const urlParams = new URLSearchParams(window.location.search);
      const rankType = urlParams.get('rankType') || urlParams.get('rank_type') || urlParams.get('type');
      const category = urlParams.get('category') || urlParams.get('cat');



      // 内容榜分类 (hot_content) - 多种URL模式
      if (normalizedUrl.includes('/hot_content/') || normalizedUrl.includes('hot_content') ||
        normalizedUrl.includes('content_rank') || category === 'content') {
        // 短视频榜
        if (normalizedUrl.includes('/short_video') || normalizedUrl.includes('short_video') ||
          normalizedUrl.includes('shortvideo') || rankType === 'short_video') {
          return {
            category: 'hot_content',
            type: 'short_video',
            categoryName: '内容榜',
            typeName: '短视频榜',
            name: '内容榜-短视频榜'
          };
        }
        // 图文榜
        else if (normalizedUrl.includes('/picture_content') || normalizedUrl.includes('picture_content') ||
          normalizedUrl.includes('image_text') || normalizedUrl.includes('picture') ||
          rankType === 'picture_content' || rankType === 'image') {
          return {
            category: 'hot_content',
            type: 'picture_content',
            categoryName: '内容榜',
            typeName: '图文榜',
            name: '内容榜-图文榜'
          };
        }
        // 商品卡榜
        else if (normalizedUrl.includes('/product_card') || normalizedUrl.includes('product_card') ||
          normalizedUrl.includes('product') || rankType === 'product_card' ||
          rankType === 'product') {
          return {
            category: 'hot_content',
            type: 'product_card',
            categoryName: '内容榜',
            typeName: '商品卡榜',
            name: '内容榜-商品卡榜'
          };
        }
      }

      // 达人榜分类 (aweme) - 多种URL模式
      else if (normalizedUrl.includes('/aweme/') || normalizedUrl.includes('aweme') ||
        normalizedUrl.includes('influencer') || normalizedUrl.includes('creator') ||
        category === 'aweme' || category === 'influencer') {
        // 电商行业榜
        if (normalizedUrl.includes('/retailers') || normalizedUrl.includes('retailers') ||
          normalizedUrl.includes('ecommerce') || normalizedUrl.includes('retail') ||
          rankType === 'retailers' || rankType === 'ecommerce') {
          return {
            category: 'aweme',
            type: 'Retailers',
            categoryName: '达人榜',
            typeName: '电商行业榜',
            name: '达人榜-电商行业榜'
          };
        }
        // 应用下载榜
        else if (normalizedUrl.includes('/app') || normalizedUrl.includes('app') ||
          normalizedUrl.includes('application') || rankType === 'app' ||
          rankType === 'application') {
          return {
            category: 'aweme',
            type: 'App',
            categoryName: '达人榜',
            typeName: '应用下载榜',
            name: '达人榜-应用下载榜'
          };
        }
        // 线索收集榜
        else if (normalizedUrl.includes('/clue') || normalizedUrl.includes('clue') ||
          normalizedUrl.includes('lead') || rankType === 'clue' ||
          rankType === 'lead') {
          return {
            category: 'aweme',
            type: 'Clue',
            categoryName: '达人榜',
            typeName: '线索收集榜',
            name: '达人榜-线索收集榜'
          };
        }
        // 短视频主题榜
        else if (normalizedUrl.includes('/shortvideo') || normalizedUrl.includes('shortvideo') ||
          normalizedUrl.includes('short_video_theme') || rankType === 'shortvideo' ||
          rankType === 'short_video_theme') {
          return {
            category: 'aweme',
            type: 'ShortVideo',
            categoryName: '达人榜',
            typeName: '短视频主题榜',
            name: '达人榜-短视频主题榜'
          };
        }
      }

      // 热词热点榜分类 (ranking) - 多种URL模式
      else if (normalizedUrl.includes('/ranking/') || normalizedUrl.includes('ranking') ||
        normalizedUrl.includes('hotword') || normalizedUrl.includes('hot_word') ||
        normalizedUrl.includes('trend') || category === 'ranking' ||
        category === 'hotword') {









        // 营销热点榜 - 使用更精确的匹配





        if (normalizedUrl.includes('/list') || normalizedUrl.includes('/ranking/list') ||
          normalizedUrl.includes('热点事件榜')) {
          return {
            category: 'ranking',
            type: 'marketing_hot_pots',
            categoryName: '营销热点榜',
            typeName: '营销热点榜',
            name: '热词热点榜-营销热点榜'
          };
        }
        // 创作热点榜
        else if (normalizedUrl.includes('/yuntu_lite/ranking/hot_word_point/hottopic') || normalizedUrl.includes('/ranking/hot_word_point/hottopic') ||
          rankType === 'hot_topic' || rankType === 'topic') {
          return {
            category: 'ranking',
            type: 'hot_topic',
            categoryName: '创作热点榜',
            typeName: '创作热点榜',
            name: '热词热点榜-创作热点榜'
          };
        } else if (normalizedUrl.includes('/yuntu_lite/ranking/hot_word_point/hotwords') || normalizedUrl.includes('/ranking/hot_word_point/hotwords') ||
          rankType === 'hot_word' || rankType === 'keyword') {
          // 默认为创作热词榜
          return {
            category: 'ranking',
            type: 'hot_words',
            categoryName: '创作热词榜',
            typeName: '创作热词榜',
            name: '创作热词榜-创作热词榜'
          };
        } else {
          // 默认为热词榜
          return {
            category: 'ranking',
            type: 'hot_word',
            categoryName: '热词热点榜',
            typeName: '热词榜',
            name: '热词热点榜-热词榜'
          };
        }
      }

      // 如果有rank_list关键词但不匹配上述分类，尝试通用判断
      else if (normalizedUrl.includes('/rank_list/')) {


        // 尝试从路径片段中提取更多信息
        const pathSegments = normalizedUrl.split('/');
        const rankListIndex = pathSegments.indexOf('rank_list');

        if (rankListIndex >= 0 && rankListIndex < pathSegments.length - 1) {
          const nextSegment = pathSegments[rankListIndex + 1];


          // 根据下一个路径段判断
          if (nextSegment === 'hot_content' && rankListIndex < pathSegments.length - 2) {
            const contentType = pathSegments[rankListIndex + 2];


            if (contentType === 'short_video') {
              return {
                category: 'hot_content',
                type: 'short_video',
                categoryName: '内容榜',
                typeName: '短视频榜',
                name: '内容榜-短视频榜'
              };
            } else if (contentType === 'picture_content') {
              return {
                category: 'hot_content',
                type: 'picture_content',
                categoryName: '内容榜',
                typeName: '图文榜',
                name: '内容榜-图文榜'
              };
            } else if (contentType === 'product_card') {
              return {
                category: 'hot_content',
                type: 'product_card',
                categoryName: '内容榜',
                typeName: '商品卡榜',
                name: '内容榜-商品卡榜'
              };
            }
          } else if (nextSegment === 'aweme' && rankListIndex < pathSegments.length - 2) {
            const awemeType = pathSegments[rankListIndex + 2];


            if (awemeType === 'Retailers') {
              return {
                category: 'aweme',
                type: 'Retailers',
                categoryName: '达人榜',
                typeName: '电商行业榜',
                name: '达人榜-电商行业榜'
              };
            } else if (awemeType === 'App') {
              return {
                category: 'aweme',
                type: 'App',
                categoryName: '达人榜',
                typeName: '应用下载榜',
                name: '达人榜-应用下载榜'
              };
            } else if (awemeType === 'Clue') {
              return {
                category: 'aweme',
                type: 'Clue',
                categoryName: '达人榜',
                typeName: '线索收集榜',
                name: '达人榜-线索收集榜'
              };
            } else if (awemeType === 'ShortVideo') {
              return {
                category: 'aweme',
                type: 'ShortVideo',
                categoryName: '达人榜',
                typeName: '短视频主题榜',
                name: '达人榜-短视频主题榜'
              };
            }
          }
        }


      }


      return null;
    } catch (error) {
      return null;
    }
  }

  // 基于页面内容识别榜单类型（备用方法）
  identifyRankingTypeByContent() {
    try {


      // 优先检查当前URL，即使在备用方法中也应该先基于URL判断
      const currentUrl = window.location.href.toLowerCase();

      // 基于URL路径进行判断（更可靠）
      if (currentUrl.includes('/hot_content/')) {
        if (currentUrl.includes('short_video')) {
          return {
            category: 'hot_content',
            type: 'short_video',
            categoryName: '内容榜',
            typeName: '短视频榜',
            name: '内容榜-短视频榜'
          };
        } else if (currentUrl.includes('picture_content')) {
          return {
            category: 'hot_content',
            type: 'picture_content',
            categoryName: '内容榜',
            typeName: '图文榜',
            name: '内容榜-图文榜'
          };
        } else if (currentUrl.includes('product_card')) {
          return {
            category: 'hot_content',
            type: 'product_card',
            categoryName: '内容榜',
            typeName: '商品卡榜',
            name: '内容榜-商品卡榜'
          };
        }
      } else if (currentUrl.includes('/aweme/')) {
        if (currentUrl.includes('retailers')) {
          return {
            category: 'aweme',
            type: 'Retailers',
            categoryName: '达人榜',
            typeName: '电商行业榜',
            name: '达人榜-电商行业榜'
          };
        } else if (currentUrl.includes('app')) {
          return {
            category: 'aweme',
            type: 'App',
            categoryName: '达人榜',
            typeName: '应用下载榜',
            name: '达人榜-应用下载榜'
          };
        } else if (currentUrl.includes('clue')) {
          return {
            category: 'aweme',
            type: 'Clue',
            categoryName: '达人榜',
            typeName: '线索收集榜',
            name: '达人榜-线索收集榜'
          };
        } else if (currentUrl.includes('shortvideo')) {
          return {
            category: 'aweme',
            type: 'ShortVideo',
            categoryName: '达人榜',
            typeName: '短视频主题榜',
            name: '达人榜-短视频主题榜'
          };
        }
      } else if (currentUrl.includes('/ranking/')) {
        if (currentUrl.includes('hot_word')) {
          return {
            category: 'ranking',
            type: 'hot_word',
            categoryName: '热词热点榜',
            typeName: '热词榜',
            name: '热词热点榜-热词榜'
          };
        } else if (currentUrl.includes('hot_topic')) {
          return {
            category: 'ranking',
            type: 'hot_topic',
            categoryName: '热词热点榜',
            typeName: '热点榜',
            name: '热词热点榜-热点榜'
          };
        }
      }

      // 如果URL判断失败，再尝试页面标题作为备用
      const title = document.title.toLowerCase();


      if (title.includes('短视频')) {
        if (title.includes('达人')) {
          return {
            category: 'aweme',
            type: 'ShortVideo',
            categoryName: '达人榜',
            typeName: '短视频主题榜',
            name: '达人榜-短视频主题榜'
          };
        } else {
          return {
            category: 'hot_content',
            type: 'short_video',
            categoryName: '内容榜',
            typeName: '短视频榜',
            name: '内容榜-短视频榜'
          };
        }
      } else if (title.includes('图文')) {
        return {
          category: 'hot_content',
          type: 'picture_content',
          categoryName: '内容榜',
          typeName: '图文榜',
          name: '内容榜-图文榜'
        };
      } else if (title.includes('商品')) {
        return {
          category: 'hot_content',
          type: 'product_card',
          categoryName: '内容榜',
          typeName: '商品卡榜',
          name: '内容榜-商品卡榜'
        };
      } else if (title.includes('达人')) {
        return {
          category: 'aweme',
          type: 'Retailers',
          categoryName: '达人榜',
          typeName: '达人榜',
          name: '达人榜-达人榜'
        };
      } else if (title.includes('热词') || title.includes('热点')) {
        return {
          category: 'ranking',
          type: 'hot_word',
          categoryName: '热词热点榜',
          typeName: '热词榜',
          name: '热词热点榜-热词榜'
        };
      }

      // 默认返回内容榜-短视频榜

      return {
        category: 'hot_content',
        type: 'short_video',
        categoryName: '内容榜',
        typeName: '短视频榜',
        name: '内容榜-短视频榜'
      };
    } catch (error) {
      return {
        category: 'hot_content',
        type: 'short_video',
        categoryName: '内容榜',
        typeName: '短视频榜',
        name: '内容榜-短视频榜'
      };
    }
  }

  // 开始获取数据
  async startExtraction(maxCount = 100) {
    if (this.isExtracting) {
      this.showNotification('获取正在进行中...', 'warning');
      return;
    }

    this.isExtracting = true;
    this.maxExtractCount = maxCount;
    this.extractCount = 0;
    this.currentPage = 1;

    try {
      // 在开始获取前清空持久化数据
      await this.clearExtractedData();

      this.showNotification('开始获取榜单数据...', 'info');
      this.sendProgressUpdate(0, '准备开始获取...');

      // 检测页面类型
      const pageType = this.detectPageType();


      if (!pageType.isRankingPage) {
        throw new Error('请在巨量引擎榜单页面使用此功能');
      }

      // 详细打印页面类型信息，帮助调试


      // 根据不同的榜单类型选择对应的获取方法
      const { category, type } = pageType.rankingType;

      if (category === 'hot_content') {
        // 内容榜分类
        if (type === 'short_video') {
          // 短视频榜使用通用获取逻辑

          await this.extractRankingData(pageType.rankingType);
        } else if (type === 'picture_content') {
          // 图文榜使用专门的获取逻辑

          await this.extractImageTextRankingData(pageType.rankingType);
        } else if (type === 'product_card') {
          // 商品卡榜 - 待开发

          await this.extractProductCardRankingData(pageType.rankingType);
        }
      } else if (category === 'aweme') {
        // 达人榜分类
        if (type === 'Retailers') {
          // 电商行业榜 - 待开发

          await this.extractRetailersRankingData(pageType.rankingType);
        } else if (type === 'App') {
          // 应用下载榜 - 待开发

          await this.extractAppRankingData(pageType.rankingType);
        } else if (type === 'Clue') {
          // 线索收集榜 - 待开发

          await this.extractClueRankingData(pageType.rankingType);
        } else if (type === 'ShortVideo') {
          // 短视频主题榜 - 待开发

          await this.extractInfluencerVideoRankingData(pageType.rankingType);
        }
      } else if (category === 'ranking') {
        // 热词热点榜分类
        if (type === 'marketing_hot_pots') {
          // 营销热点榜

          await this.extractMarketingHotPotsRankingData(pageType.rankingType);
        } else if (type === 'hot_topic') {
          // 创作热点榜 - 待开发

          await this.extractHotTopicRankingData(pageType.rankingType);
        } else if (type === 'hot_words') {
          // 创作热词榜 - 待开发

          await this.extractHotWordsRankingData(pageType.rankingType);
        }
      } else {
        // 未知类型，使用通用获取逻辑

        await this.extractRankingData(pageType.rankingType);
      }

      // 获取完成
      this.isExtracting = false;
      this.sendExtractionComplete();
      this.showNotification(`获取完成！共获取 ${this.extractCount} 条数据`, 'success');
    } catch (error) {
      this.isExtracting = false;
      this.sendErrorUpdate(error.message);
      this.showNotification(`获取失败: ${error.message}`, 'error');
    }
  }

  // 清空已提取的数据
  async clearExtractedData() {
    try {


      // 清空内存中的数据
      this.extractedData = {
        hot_content: {
          short_video: [],      // 短视频榜
          picture_content: [],  // 图文榜
          product_card: []      // 商品卡榜
        },
        aweme: {
          Retailers: [],        // 电商行业榜
          App: [],              // 应用下载榜
          Clue: [],             // 线索收集榜
          ShortVideo: []        // 短视频主题榜
        },
        ranking: {
          hot_word: [],         // 热词榜
          hot_topic: [],        // 热点榜
          marketing_hot_pots: [] // 营销热点榜
        }
      };

      // 清空存储中的数据
      // 获取所有与巨量引擎相关的键
      const keys = await new Promise(resolve => {
        chrome.storage.local.get(null, (items) => {
          const oceanengineKeys = Object.keys(items).filter(key =>
            key.startsWith('oceanengine_')
          );
          resolve(oceanengineKeys);
        });
      });

      // 如果有键需要删除
      if (keys.length > 0) {
        await new Promise(resolve => {
          chrome.storage.local.remove(keys, () => {

            resolve();
          });
        });
      } else {

      }

      this.showNotification('已清空之前的获取数据', 'info');
    } catch (error) {
      // 继续执行，不因为清空失败而中断获取
    }
  }

  // 停止获取
  stopExtraction() {
    this.isExtracting = false;
    this.showNotification('已停止获取', 'info');
    this.sendProgressUpdate(0, '已停止获取');
  }

  // 获取短视频榜
  async extractRankingData(rankingType) {


    try {

      const extractedData = await this.extractVideoRankingData(rankingType);

      if (extractedData.length > 0) {

        this.saveExtractedData(rankingType, extractedData);
      } else {

      }

      return extractedData;

    } catch (error) {
      this.sendErrorUpdate(`短视频榜数据提取失败: ${error.message}`);
      return [];
    }
  }

  // 提取当前页面数据
  async extractCurrentPageData(rankingType) {
    const results = [];

    try {


      // 尝试多种表格选择器
      const tableSelectors = [
        '.rank-list-table-body',
        '.rank-list-Table-Body',
        'tbody',
        '.table-body',
        '[role="rowgroup"]'
      ];

      let tableBody = null;
      for (const selector of tableSelectors) {
        const element = document.querySelector(selector);
        if (element) {

          tableBody = element;
          break;
        }
      }

      if (!tableBody) {
        console.warn('未找到表格主体，尝试直接查找行元素');

        // 如果找不到表格主体，直接尝试查找行元素
        const rowSelectors = [
          '.rank-list-table-row',
          '.rank-list-Table-Row',
          'tr:not(:first-child)',
          '[role="row"]'
        ];

        for (const selector of rowSelectors) {
          const rows = document.querySelectorAll(selector);
          if (rows && rows.length > 0) {


            for (const row of rows) {
              if (!this.isExtracting) break;

              const rowData = this.extractRowData(row, rankingType);
              if (rowData) {
                results.push(rowData);
              }
            }

            return results;
          }
        }

        throw new Error('未找到表格数据');
      }

      // 获取所有数据行 - 尝试多种行选择器
      const rowSelectors = [
        '.rank-list-table-row',
        '.rank-list-Table-Row',
        'tr',
        '[role="row"]'
      ];

      let rows = [];
      for (const selector of rowSelectors) {
        const elements = tableBody.querySelectorAll(selector);
        if (elements && elements.length > 0) {

          rows = Array.from(elements);
          break;
        }
      }

      if (rows.length === 0) {
        console.warn('未找到数据行，尝试直接获取tableBody的子元素');
        rows = Array.from(tableBody.children);
      }



      for (const row of rows) {
        if (!this.isExtracting) break;

        const rowData = this.extractRowData(row, rankingType);
        if (rowData) {
          results.push(rowData);
        }
      }

    } catch (error) {
      console.error('提取页面数据失败:', error);
    }


    return results;
  }

  // 提取表格行数据
  extractRowData(row, rankingType) {
    try {




      // 尝试多种单元格选择器
      const cellSelectors = [
        '.rank-list-table-cell-body',
        '.rank-list-Table-Cell',
        'td',
        '[role="cell"]'
      ];

      let cells = [];
      // 尝试所有选择器
      for (const selector of cellSelectors) {
        const elements = row.querySelectorAll(selector);
        if (elements && elements.length > 0) {
          cells = Array.from(elements);

          break;
        }
      }

      // 如果没有找到单元格，尝试直接使用子元素
      if (cells.length === 0) {
        cells = Array.from(row.children);

      }

      // 放宽验证条件：至少需要1个单元格
      if (cells.length < 1) {

        return null;
      }

      // 基础数据结构
      const data = {
        timestamp: Date.now(),
        pageUrl: window.location.href,
        rankingType: rankingType,
        rank: this.extractRank(cells[0])
      };



      // 根据单元格数量动态提取数据
      if (cells.length > 1) {
        data.content = this.extractVideoContent(cells[1]);

      }

      if (cells.length > 2) {
        // 尝试提取关键词，如果失败则提取为普通文本
        try {
          data.keywords = this.extractKeywords(cells[2]);
        } catch (e) {
          data.keywords = [this.extractText(cells[2])];
        }

      }

      // 动态提取其他指标
      const statFields = ['exposureIndex', 'completionRate', 'interactionRate', 'likes', 'comments', 'shares'];
      for (let i = 3; i < Math.min(cells.length, 9); i++) {
        const fieldIndex = i - 3;
        if (fieldIndex < statFields.length) {
          data[statFields[fieldIndex]] = this.extractText(cells[i]);
        }
      }

      // 检查数据质量
      const hasValidRank = data.rank > 0;
      const hasValidTitle = data.content && data.content.title && data.content.title.trim().length > 0;
      const hasValidContent = data.content || data.keywords || Object.keys(data).length > 4;






      // 放宽验证条件：只要有排名或有标题或有任何有效内容就保留
      if (hasValidRank || hasValidTitle || hasValidContent) {


        return data;
      } else {

        return null;
      }

    } catch (error) {
      console.error('提取行数据失败:', error);
      return null;
    }
  }

  // 提取排名信息
  extractRank(cell) {
    try {


      // 方法1: 查找排名图片，根据图片特征识别前三名
      const rankImg = cell.querySelector('img[src*="medal"], img[src*="rank"], .useTableColumnsComponents__IconImg-eCkXAM');
      if (rankImg) {
        const src = rankImg.src || '';

        // 通过图片URL或颜色特征识别前三名
        // 第一名通常是金色
        if (src.includes('FEB332') || src.includes('FE8221') || src.includes('gold') ||
          src.includes('rank1') || src.includes('medal1') || src.includes('first')) {

          return 1;
        }
        // 第二名通常是银色
        else if (src.includes('C6CBD1') || src.includes('F7F6F9') || src.includes('silver') ||
          src.includes('rank2') || src.includes('medal2') || src.includes('second')) {

          return 2;
        }
        // 第三名通常是铜色
        else if (src.includes('CD7F32') || src.includes('B87333') || src.includes('bronze') ||
          src.includes('rank3') || src.includes('medal3') || src.includes('third')) {

          return 3;
        }


      }

      // 方法2: 更精确地检查单元格是否包含纯数字或带有标号的数字
      const cellText = cell.textContent.trim();

      // 检查是否为纯数字
      if (/^\d+$/.test(cellText)) {
        const rank = parseInt(cellText);

        return rank;
      }

      // 检查单元格文本中的数字
      const rankMatch = cellText.match(/^(\d+)/);
      if (rankMatch) {
        const rank = parseInt(rankMatch[0]);

        return rank;
      }

      // 方法3: 查找具有特定CSS类的排名元素
      const rankSelectors = [
        '[class*="rank-num"]',
        '[class*="rankNum"]',
        '[class*="rank-value"]',
        '[class*="rankValue"]',
        '[class*="rank"]',
        '[class*="Rank"]'
      ];

      for (const selector of rankSelectors) {
        const rankElement = cell.querySelector(selector);
        if (rankElement && rankElement.textContent) {
          const rankText = rankElement.textContent.trim();
          const rankMatch = rankText.match(/\d+/);
          if (rankMatch) {
            const rank = parseInt(rankMatch[0]);

            return rank;
          }
        }
      }

      // 方法4: 检查单元格是否包含特定排名文本
      if (cellText.includes('TOP') || cellText.includes('top')) {
        const topMatch = cellText.match(/TOP\s*(\d+)|top\s*(\d+)/i);
        if (topMatch) {
          const rank = parseInt(topMatch[1] || topMatch[2]);

          return rank;
        }
      }

      // 方法5: 从排名图标的类名中提取
      const rankIconElement = cell.querySelector('[class*="rank-"]');
      if (rankIconElement) {
        // 尝试从类名中提取排名信息
        const rankClasses = Array.from(rankIconElement.classList).filter(cls => cls.includes('rank-'));
        for (const cls of rankClasses) {
          const rankMatch = cls.match(/rank-(\d+)/);
          if (rankMatch) {
            const rank = parseInt(rankMatch[1]);

            return rank;
          }
        }
      }

      // 方法6: 安全获取行索引，避免parentNode为null的情况
      try {
        const tr = cell.closest('tr');
        if (tr && tr.parentNode) {
          const siblings = Array.from(tr.parentNode.children).filter(node =>
            node.tagName === 'TR' ||
            node.classList.contains('rank-list-table-row')
          );
          const rowIndex = siblings.indexOf(tr);
          if (rowIndex >= 0) {

            return rowIndex + 1;
          }
        }
      } catch (e) {
        console.warn('获取行索引失败:', e);
      }


      return 0;
    } catch (error) {
      return 0;
    }
  }

  // 提取视频内容信息
  extractVideoContent(cell) {
    try {
      const contentData = {
        title: '',
        coverUrl: '',
        videoUrl: '',
        duration: ''
      };

      // 提取标题
      const titleSelectors = [
        '[style*="display: -webkit-box"]',
        '[style*="overflow: hidden"]',
        '.content-title',
        '.video-title',
        '.title',
        'h3', 'h4', 'p', 'span'
      ];

      for (const selector of titleSelectors) {
        const titleElement = cell.querySelector(selector);
        if (titleElement && titleElement.textContent.trim()) {
          contentData.title = this.extractText(titleElement);
          if (contentData.title.length > 10) { // 确保获取到有意义的标题
            break;
          }
        }
      }

      // 提取封面图
      const coverElements = cell.querySelectorAll('img');
      for (const img of coverElements) {
        if (img.src && !img.src.includes('data:image') && img.src.includes('http')) {
          contentData.coverUrl = img.src;
          break;
        }
      }

      // 提取视频时长
      const durationSelectors = [
        '.dur',
        '.duration',
        '[class*="duration"]',
        '[class*="time"]'
      ];

      for (const selector of durationSelectors) {
        const durationElement = cell.querySelector(selector);
        if (durationElement) {
          contentData.duration = this.extractText(durationElement);
          break;
        }
      }

      // 提取视频链接
      const linkElement = cell.querySelector('a');
      if (linkElement && linkElement.href) {
        contentData.videoUrl = linkElement.href;
      }

      return contentData;
    } catch (error) {
      return {
        title: '',
        coverUrl: '',
        videoUrl: '',
        duration: ''
      };
    }
  }

  extractVideoContentFromCell(cell) {
    try {


      // 完全按照old.js的逻辑
      const content = {
        title: '',
        duration: '',
        coverUrl: '',
        videoUrl: ''
      };

      // 提取视频标题 - 按照old.js的逻辑
      const titleElement = cell.querySelector('[style*="display: -webkit-box"]');
      if (titleElement) {
        content.title = titleElement.textContent.trim();

      }

      // 提取视频时长 - 按照old.js的逻辑
      const durationElement = cell.querySelector('.dur');
      if (durationElement) {
        content.duration = durationElement.textContent.replace('视频时长：', '').trim();

      }

      // 提取封面图片 - 按照old.js的逻辑
      const coverElement = cell.querySelector('.turtle-video-player-square-poster');
      if (coverElement) {
        content.coverUrl = coverElement.src;

      }


      return content;
    } catch (error) {
      return {
        title: '',
        duration: '',
        coverUrl: '',
        videoUrl: ''
      };
    }
  }

  extractKeywordsFromCell(cell) {
    try {


      const keywords = [];

      // 完全按照old.js的逻辑
      const tagElements = cell.querySelectorAll('.useTableColumns__Tag-iEgeAq');

      tagElements.forEach(tag => {
        // 查找标签内的文本内容 - 按照old.js的逻辑
        const textElement = tag.querySelector('[style*="display: -webkit-box"]');
        if (textElement) {
          const keyword = textElement.textContent.trim();
          if (keyword) {
            keywords.push(keyword);
          }
        }
      });


      return keywords;
    } catch (error) {
      return [];
    }
  }

  // 从元素中提取文本内容
  extractText(cell) {
    try {
      if (!cell) return '';

      // 完全按照old.js的逻辑 - 查找span元素中的文本
      const spanElement = cell.querySelector('span');
      if (spanElement) {
        return spanElement.textContent.trim();
      }

      // 备用方法：直接提取单元格文本 - 按照old.js的逻辑
      return cell.textContent.trim();
    } catch (error) {
      return '';
    }
  }

  // 转到下一页
  async goToNextPage() {
    try {


      // 尝试各种可能的下一页按钮选择器
      const nextButtonSelectors = [
        'button[aria-label*="下一页"]',
        'button[title*="下一页"]',
        'button[aria-label*="next"]',
        'button[aria-label*="Next"]',
        'button[title*="next"]',
        'button[title*="Next"]',
        '.pager-next:not(.disabled)',
        '.next:not(.disabled)',
        '[data-page="next"]:not(.disabled)',
        '[aria-label="Next Page"]:not(.disabled)',
        '.next-page:not(.disabled)',
        '[class*="next"]:not([class*="disabled"])',
        '[class*="Next"]:not([class*="disabled"])',
        '.rank-list-icon-right'
      ];

      let nextButton = null;

      // 特殊处理图文榜单的分页
      try {
        // 检查是否是图文榜单页面
        const isPictureContent = document.querySelector('.rank-list-pager') !== null;

        if (isPictureContent) {

          // 图文榜单特定的下一页按钮选择器
          const pictureNextButton = document.querySelector('.rank-list-icon.rank-list-icon-right');

          if (pictureNextButton) {
            const parentLi = pictureNextButton.closest('li');
            if (parentLi && !parentLi.classList.contains('rank-list-pager-item-disabled')) {

              pictureNextButton.click();
              this.currentPage++;

              return true;
            } else {

            }
          }
        }
      } catch (e) {
        console.warn('图文榜单分页处理失败:', e);
      }

      // 先尝试直接选择器
      for (const selector of nextButtonSelectors) {
        try {
          nextButton = document.querySelector(selector);
          if (nextButton && !nextButton.classList.contains('disabled') &&
            !nextButton.hasAttribute('disabled')) {

            nextButton.click();
            this.currentPage++;

            return true;
          }
        } catch (e) {
          console.warn(`选择器 ${selector} 失败:`, e);
        }
      }

      // 如果直接选择器没找到，尝试查找包含特定文本的元素
      try {
        const buttons = Array.from(document.querySelectorAll('button, a, li'));
        nextButton = buttons.find(btn => {
          const text = btn.textContent.trim();
          return text.includes('下一页') || text.includes('next') || text.includes('Next');
        });

        if (nextButton && !nextButton.classList.contains('disabled') &&
          !nextButton.hasAttribute('disabled')) {

          nextButton.click();
          this.currentPage++;

          return true;
        }
      } catch (e) {
        console.warn('通过文本查找下一页按钮失败:', e);
      }

      // 尝试查找包含页码的元素
      const pageNumberElements = document.querySelectorAll('[class*="page"], [class*="Page"]');


      for (const el of pageNumberElements) {
        const text = el.textContent.trim();
        if (text === String(this.currentPage + 1)) {

          el.click();
          this.currentPage++;

          return true;
        }
      }


      return false;
    } catch (error) {
      console.error('翻页失败:', error);
      return false;
    }
  }

  // 等待页面加载
  async waitForPageLoad() {

    // 等待表格数据更新 - 支持多种榜单类型
    try {
      const tableSelectors = [
        '.rank-list-Table-Body .rank-list-Table-Row',
        'tbody tr[role="row"]',
        '.rank-list-table-body .rank-list-table-row',
        'table tbody tr',
        '.rank-list-Table tr'
      ];

      let elementFound = false;
      for (const selector of tableSelectors) {
        try {
          await this.waitForElement(selector, 500);

          elementFound = true;
          break;
        } catch (e) {

        }
      }

      if (!elementFound) {

      }
    } catch (error) {

    }
  }

  // 等待元素出现
  async waitForElement(selector, timeout = 500) {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver(() => {
        const element = document.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`元素 ${selector} 未找到`));
      }, timeout);
    });
  }

  // 保存提取的数据
  saveExtractedData(rankingType, data) {
    const { category, type } = rankingType;

    // 确保存储结构中存在对应的分类和类型
    if (!this.extractedData[category]) {
      console.warn(`未知的榜单分类: ${category}，创建默认结构`);
      this.extractedData[category] = {};
    }

    if (!this.extractedData[category][type]) {
      console.warn(`未知的榜单类型: ${category}/${type}，创建数组`);
      this.extractedData[category][type] = [];
    }

    // 对数据进行处理和增强
    const enhancedData = data.map(item => {
      // 确保排名信息正确
      if (!item.rank && item.rank !== 0) {
        // 如果排名字段不存在，使用索引作为排名
        item.rank = data.indexOf(item) + 1;

      } else if (item.rank === 0) {
        // 如果排名为0，也使用索引作为排名
        item.rank = data.indexOf(item) + 1;

      }

      // 特别记录前三名的数据
      if (item.rank <= 3) {
        console.log(`保存Top ${item.rank} 数据:`, JSON.stringify({
          rank: item.rank,
          title: item.content?.title || item.title,
          isTopThree: true
        }));
      }

      // 增强数据，添加排名状态
      return {
        ...item,
        isTopThree: item.rank <= 3,
        rankPosition: item.rank <= 3 ? `top-${item.rank}` : 'normal',
        rankingCategory: category,
        rankingType: type
      };
    });

    // 保存到内存中，部分榜单数据采用替换模式而非追加
    if (category === 'hot_content' && type === 'short_video') {
      // 短视频榜采用替换模式
      this.extractedData[category][type] = enhancedData;
    } else if (category === 'ranking' && type === 'marketing_hot_pots') {
      // 营销热点榜采用替换模式
      this.extractedData[category][type] = enhancedData;
    } else if (category === 'aweme' && type === 'Clue') {
      // 线索收集榜采用替换模式
      this.extractedData[category][type] = enhancedData;
    } else {
      // 其他榜单采用追加模式
      this.extractedData[category][type].push(...enhancedData);
    }

    // 保存到本地存储
    const storageKey = `oceanengine_${category}_${type}_${Date.now()}`;
    chrome.storage.local.set({
      [storageKey]: {
        timestamp: Date.now(),
        pageUrl: window.location.href,
        rankingType: rankingType,
        data: enhancedData,
        extractedCount: enhancedData.length
      }
    });



  }

  // 发送进度更新
  sendProgressUpdate(progress, status) {
    chrome.runtime.sendMessage({
      action: 'oceanEngineProgress',
      data: {
        progress: progress,
        status: status,
        extractCount: this.extractCount,
        maxCount: this.maxExtractCount,
        currentPage: this.currentPage
      }
    });
  }

  // 发送获取完成消息
  sendExtractionComplete() {
    chrome.runtime.sendMessage({
      action: 'oceanEngineComplete',
      data: {
        extractCount: this.extractCount,
        extractedData: this.extractedData,
        timestamp: Date.now()
      }
    });
  }

  // 发送错误消息
  sendErrorUpdate(error) {
    chrome.runtime.sendMessage({
      action: 'oceanEngineError',
      error: error
    });
  }

  // 显示通知
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `oceanengine-notification ${type}`;
    notification.textContent = message;

    notification.style.cssText = `
      position: fixed;
      top: 80px;
      right: 20px;
      z-index: 10000;
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      max-width: 300px;
      word-wrap: break-word;
      animation: slideIn 0.3s ease;
    `;

    // 设置背景色
    switch (type) {
      case 'success':
        notification.style.background = '#2ed573';
        break;
      case 'error':
        notification.style.background = '#ff4757';
        break;
      case 'warning':
        notification.style.background = '#ffa502';
        break;
      default:
        notification.style.background = '#3742fa';
    }

    document.body.appendChild(notification);

    // 3秒后自动移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // 设置消息监听器
  setupMessageListener() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {


      switch (request.action) {
        case 'getOceanEnginePageStats':
          sendResponse(this.getPageStats());
          break;

        case 'startOceanEngineExtraction':
          const maxCount = request.maxCount || 100;
          this.startExtraction(maxCount);
          sendResponse({ success: true, message: `开始获取榜单数据，目标: ${maxCount} 条` });
          break;

        case 'stopOceanEngineExtraction':
          this.stopExtraction();
          sendResponse({ success: true });
          break;

        case 'getOceanEnginePageInfo':
          sendResponse({
            success: true,
            pageInfo: this.getPageInfo()
          });
          break;

        case 'clearOceanEngineHistory':
          this.clearHistory();
          sendResponse({ success: true });
          break;



        default:
          sendResponse({ success: false, error: '未知操作' });
      }

      return true;
    });
  }

  // 获取页面统计信息
  getPageStats() {
    const rankingType = this.detectPageType();
    const tableRows = document.querySelectorAll('.rank-list-table-body .rank-list-table-row');

    return {
      pageType: rankingType ? 'ranking' : 'unknown',
      rankingType: rankingType,
      itemCount: tableRows.length,
      currentUrl: window.location.href,
      pageTitle: document.title,
      isExtracting: this.isExtracting,
      extractCount: this.extractCount
    };
  }

  // 获取页面信息
  getPageInfo() {
    // 获取页面类型检测结果
    const pageTypeResult = this.detectPageType();


    // 提取榜单类型信息
    let rankingType = null;
    if (pageTypeResult && pageTypeResult.isRankingPage && pageTypeResult.rankingType) {
      rankingType = pageTypeResult.rankingType;
    }


    // 检查是否存在榜单相关元素
    const hasRankingElements = document.querySelector('.yuntu_st_main-menu-item-active') ||
      document.querySelector('[data-log-value]') ||
      document.querySelector('.rank-list-table-body') ||
      document.querySelector('[data-log-module="热点事件榜"]'); // 添加营销热点榜容器检测

    // 检查页面URL是否包含榜单相关关键词
    const url = window.location.href;
    const hasRankingInURL = url.includes('rank') ||
      url.includes('榜单') ||
      url.includes('榜') ||
      url.includes('ranking'); // 添加ranking关键词

    // 综合判断是否为榜单页面
    const isRankingPage = pageTypeResult?.isRankingPage || hasRankingElements || hasRankingInURL;

    const result = {
      pageType: 'oceanengine',
      subType: isRankingPage ? 'ranking' : 'unknown',
      rankingType: rankingType,
      currentUrl: window.location.href,
      pageTitle: document.title,
      isExtracting: this.isExtracting,
      hasRankingElements: !!hasRankingElements
    };


    return result;
  }

  // 清空历史数据
  clearHistory() {
    this.extractedData = {
      hot_content: { short_video: [], picture_content: [], product_card: [] },
      aweme: { Retailers: [], App: [], Clue: [], ShortVideo: [] },
      ranking: { hot_word: [], hot_topic: [], marketing_hot_pots: [] }
    };

    // 清空本地存储
    chrome.storage.local.get(null, items => {
      const keysToRemove = Object.keys(items).filter(key =>
        key.startsWith('oceanengine_')
      );
      chrome.storage.local.remove(keysToRemove);
    });

    this.showNotification('历史数据已清空', 'info');
  }




    // URL规范化测试
    const normalizedUrl = currentUrl.toLowerCase();

    // URL匹配测试





    // 页面类型检测
    const pageType = this.detectPageType();


    // DOM元素检测

    const container = document.querySelector('[data-log-module="热点事件榜"]');


    if (container) {
      const items = container.querySelectorAll('.HotMarketingRankList__Item-iEsmWe');

    }



    // 返回页面信息供popup显示
    return this.getPageInfo();
  }

  // 测试数据提取
  async testDataExtraction() {


    const pageType = this.detectPageType();
    if (!pageType) {

      return;
    }



    try {
      // 根据页面类型使用不同的测试逻辑
      if (pageType.type === 'marketing_hot_pots') {
        // 营销热点榜测试


        // 等待营销热点榜容器加载
        await this.waitForElement('[data-log-module="热点事件榜"]', 500);

        // 查找营销热点榜容器
        const container = document.querySelector('[data-log-module="热点事件榜"]');
        if (!container) {

          return;
        }

        // 查找第一个热点项进行测试
        const firstItem = container.querySelector('.HotMarketingRankList__Item-iEsmWe');
        if (firstItem) {

          const itemData = this.extractMarketingHotPotItemData(firstItem, 1);

        } else {

        }

      } else {
        // 其他榜单类型的测试（使用原有逻辑）
        await this.waitForElement('.rank-list-table-body');

        // 获取第一行数据进行测试
        const firstRow = document.querySelector('.rank-list-table-body .rank-list-table-row');
        if (firstRow) {

          const rowData = this.extractRowData(firstRow, pageType);

        } else {

        }
      }

    } catch (error) {
      console.error('测试数据提取失败:', error);
    }


  }

  // 专门处理图文榜的数据提取
  async extractImageTextRankingData(rankingType) {
    try {

      this.showNotification('正在提取图文榜数据...', 'info');

      let allExtractedData = [];
      let hasMoreData = true;

      while (hasMoreData && this.isExtracting && this.extractCount < this.maxExtractCount) {


        // 尝试多种表格选择器
        const tableSelectors = [
          '.rank-list-Table-Body',
          'tbody',
          '.table-body',
          '[role="rowgroup"]',
          '.rank-list-table-body'
        ];

        let tableBody = null;
        for (const selector of tableSelectors) {
          const element = document.querySelector(selector);
          if (element) {

            tableBody = element;
            break;
          }
        }

        if (!tableBody) {
          console.warn('未找到图文榜表格主体');
          return [];
        }

        // 提取行数据
        const rows = tableBody.querySelectorAll('tr, .rank-list-Table-Row, [role="row"]');


        if (!rows || rows.length === 0) {
          console.warn('未找到图文榜数据行');
          return [];
        }

        const pageData = [];

        // 处理每一行数据
        for (const row of rows) {
          if (this.extractCount >= this.maxExtractCount) {

            break;
          }

          const rowData = this.extractImageTextRowData(row);
          if (rowData) {
            pageData.push(rowData);
            this.extractCount++;

            // 更新进度
            const progress = Math.min(Math.round((this.extractCount / this.maxExtractCount) * 100), 100);
            this.sendProgressUpdate(progress, `已获取${this.extractCount}条数据，当前第${this.currentPage}页`);
          }
        }

        if (pageData.length > 0) {
          // 保存数据
          this.saveExtractedData(rankingType, pageData);
          allExtractedData = [...allExtractedData, ...pageData];



          // 如果数量已经达到最大获取数量，停止获取
          if (this.extractCount >= this.maxExtractCount) {

            break;
          }

          // 尝试翻页

          const hasNextPage = await this.goToNextPage();

          if (hasNextPage) {

            // 等待页面加载完成
            await this.waitForPageLoad();

            // 添加固定等待时间（1秒），确保页面元素完全加载
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 等待表格数据加载
            let tableLoaded = false;
            for (const selector of tableSelectors) {
              try {
                await this.waitForElement(selector, 1000); // 增加表格等待时间
                tableLoaded = true;

                break;
              } catch (e) {
                console.warn(`等待表格选择器 ${selector} 超时`);
              }
            }

            if (!tableLoaded) {
              console.log('表格数据加载超时，继续执行...');
            }

          } else {

            hasMoreData = false;
          }
        } else {

          hasMoreData = false;
        }
      }


      return allExtractedData;
    } catch (error) {
      this.sendErrorUpdate(`提取图文榜数据失败: ${error.message}`);
      return [];
    }
  }

  // 将文本转换为数字
  parseNumber(text) {
    try {
      if (!text) return 0;

      // 清理文本，移除非数字和特定字符
      const cleanText = String(text).replace(/[^\d.万千亿k+]/gi, '').trim();

      // 处理空字符串
      if (!cleanText) return 0;

      // 处理中文数字单位
      if (cleanText.includes('万')) {
        return parseFloat(cleanText.replace('万', '')) * 10000;
      } else if (cleanText.includes('千')) {
        return parseFloat(cleanText.replace('千', '')) * 1000;
      } else if (cleanText.includes('亿')) {
        return parseFloat(cleanText.replace('亿', '')) * 100000000;
      }

      // 处理K/k单位 (千)
      if (cleanText.toLowerCase().includes('k')) {
        return parseFloat(cleanText.toLowerCase().replace('k', '')) * 1000;
      }

      // 处理带加号的数字 (例如 1000+)
      if (cleanText.includes('+')) {
        return parseFloat(cleanText.replace('+', ''));
      }

      // 尝试直接解析数字
      return parseFloat(cleanText) || 0;
    } catch (error) {
      console.error('解析数字失败:', error, '原始文本:', text);
      return 0;
    }
  }

  // 从图文榜行中提取数据
  extractImageTextRowData(row) {
    try {
      // 所有单元格 - 使用多种选择器
      let cells = [];
      const cellSelectors = [
        '.rank-list-Table-Cell',
        'td',
        '[role="cell"]'
      ];

      // 尝试所有可能的单元格选择器
      for (const selector of cellSelectors) {
        const elements = row.querySelectorAll(selector);
        if (elements && elements.length > 0) {
          cells = Array.from(elements);
          break;
        }
      }

      if (cells.length < 5) {
        console.warn(`该行单元格数量不足: ${cells.length}，跳过`);
        return null;
      }

      // 提取排名
      const rankCell = cells[0];
      const rank = this.extractRank(rankCell);

      // 提取图文内容信息
      const contentCell = cells[1];
      const content = this.extractImageTextContent(contentCell);

      // 提取图片张数 - 如果存在的话
      const imageCount = cells.length > 2 ? this.extractText(cells[2]) : '';

      // 提取关联商品信息 - 如果存在的话
      const product = cells.length > 3 ? this.extractProductInfo(cells[3]) : { title: '', id: '', imageUrl: '' };

      // 提取各种指标 - 动态判断可用的指标
      const stats = {};

      // 记录所有可能的数据 - 确保调用正确的方法
      if (cells.length > 4) stats.exposure = this.extractText(cells[4]);
      if (cells.length > 5) stats.playRate3s = this.extractText(cells[5]);
      if (cells.length > 6) stats.playRate5s = this.extractText(cells[6]);
      if (cells.length > 7) stats.completeRate = this.extractText(cells[7]);

      // 对于需要解析为数字的值，使用安全的方式调用parseNumber
      if (cells.length > 8) {
        const likesText = this.extractText(cells[8]);
        stats.likes = typeof this.parseNumber === 'function'
          ? this.parseNumber(likesText)
          : parseInt(likesText) || 0;
      }

      if (cells.length > 9) {
        const favoritesText = this.extractText(cells[9]);
        stats.favorites = typeof this.parseNumber === 'function'
          ? this.parseNumber(favoritesText)
          : parseInt(favoritesText) || 0;
      }

      // 图片张数可能出现在不同位置
      if (imageCount) {
        stats.imageCount = imageCount;
      }

      // 构建完整的图文榜数据项
      return {
        rank,
        content,
        stats,
        product,
        isImageText: true,
        type: 'image',
        timestamp: Date.now()
      };
    } catch (error) {
      return null;
    }
  }

  // 提取图文内容信息
  extractImageTextContent(cell) {
    try {
      const content = {
        title: '',
        coverUrl: '',
        imageUrl: ''
      };

      // 提取标题 - 多种选择器
      const titleSelectors = [
        'div[style*="display: -webkit-box"]',
        'div[style*="overflow: hidden"]',
        '.ContentCell__Container-IkSrg div:not(.img)',
        '[class*="content"] div',
        '[class*="title"]'
      ];

      // 尝试所有可能的标题选择器
      for (const selector of titleSelectors) {
        const element = cell.querySelector(selector);
        if (element && element.textContent.trim()) {
          content.title = element.textContent.trim();

          break;
        }
      }

      // 提取封面图片 - 多种选择器
      const imageSelectors = [
        '.ContentCell__ImgContainer-kowHEX img',
        'img',
        '[class*="img"] img',
        '[class*="Img"] img',
        '[class*="cover"] img',
        '[class*="Cover"] img'
      ];

      // 尝试所有可能的图片选择器
      for (const selector of imageSelectors) {
        const element = cell.querySelector(selector);
        if (element && element.src) {
          content.coverUrl = element.src;
          content.imageUrl = element.src;

          break;
        }
      }

      return content;
    } catch (error) {
      return { title: '', coverUrl: '', imageUrl: '' };
    }
  }

  // 提取商品信息
  extractProductInfo(cell) {
    const productInfo = {
      title: '',
      id: '',
      image: '',
      url: ''
    };

    if (!cell) {
      console.warn('商品信息单元格为空');
      return productInfo;
    }

    try {


      // 提取商品标题 - 基于实际HTML结构
      const titleSelectors = [
        '.ellipsisText__EllipsisTextItemContainer-eQpLFK.ebmtII',
        '.ProductCell__NameContainer-eJazdt .ellipsisText__EllipsisTextItemContainer-eQpLFK',
        '[style*="color: rgb(31, 75, 217)"]',
        '.ProductCell__TitleContainer-jJdGNm span:first-child',
        '.product-title',
        '.item-title',
        'span[style*="line-height: 20px"]'
      ];

      for (const selector of titleSelectors) {
        const titleElement = cell.querySelector(selector);
        if (titleElement) {
          productInfo.title = this.extractText(titleElement).trim();
          if (productInfo.title) {

            break;
          }
        }
      }

      // 提取商品ID - 基于实际HTML结构
      const idSelectors = [
        '.id .ellipsisText__EllipsisTextItemContainer-eQpLFK.ebmtIH',
        '.id span',
        '.product-id',
        '[style*="text-align: left"]',
        'div.id span'
      ];

      for (const selector of idSelectors) {
        const idElement = cell.querySelector(selector);
        if (idElement) {
          const idText = this.extractText(idElement);
          const idMatch = idText.match(/ID[:\s]*(\d+)/i);
          if (idMatch) {
            productInfo.id = idMatch[1];

            break;
          }
        }
      }

      // 提取商品图片 - 基于实际HTML结构
      const imageSelectors = [
        '.Imgs__ImgContainer-bIvTs img',
        '.ProductCell__Container-yBvRS img',
        '.product-image img',
        'img[src*="ecombdimg.com"]',
        'img[src*="p3-aio.ecombdimg.com"]',
        'img'
      ];

      for (const selector of imageSelectors) {
        const imgElement = cell.querySelector(selector);
        if (imgElement && imgElement.src) {
          productInfo.image = imgElement.src;

          break;
        }
      }

      // 尝试从全部文本中提取商品ID（如果前面没有找到）
      if (!productInfo.id) {
        const allText = this.extractText(cell);
        const idMatch = allText.match(/ID[:\s]*(\d+)/i);
        if (idMatch) {
          productInfo.id = idMatch[1];

        }
      }

      // 如果没有标题，尝试从全部文本中提取
      if (!productInfo.title) {
        const allTextElement = cell.querySelector('.ProductCell__TitleContainer-jJdGNm, .product-info, .title');
        if (allTextElement) {
          const fullText = this.extractText(allTextElement);
          // 提取第一行作为标题（通常ID在第二行）
          const lines = fullText.split('\n').filter(line => line.trim());
          if (lines.length > 0) {
            // 找到不包含ID的行作为标题
            for (const line of lines) {
              if (!line.includes('ID:') && line.length > 5) {
                productInfo.title = line.trim();

                break;
              }
            }
          }
        }
      }

      // 最终检查
      if (!productInfo.title && !productInfo.id) {
        console.warn('❌ 未能提取到商品标题和ID');

      }

    } catch (error) {
      console.error('提取商品信息失败:', error);
    }

    return productInfo;
  }

  // 提取商品指标数据
  extractProductMetrics(cells) {
    const metrics = {
      gmvIndex: '',      // GMV指数
      price: '',         // 商品售卖价
      salesIndex: '',    // 销售量指数  
      clickIndex: '',    // 点击指数
      conversionRate: '' // 转化率
    };

    try {


      // GMV指数（第3列，索引2）
      if (cells[2]) {
        metrics.gmvIndex = this.extractText(cells[2]).trim();

      }

      // 商品售卖价（第4列，索引3）
      if (cells[3]) {
        const priceText = this.extractText(cells[3]).trim();
        metrics.price = priceText;

      }

      // 销售量指数（第5列，索引4）
      if (cells[4]) {
        metrics.salesIndex = this.extractText(cells[4]).trim();

      }

      // 点击指数（第6列，索引5）
      if (cells[5]) {
        metrics.clickIndex = this.extractText(cells[5]).trim();

      }

      // 转化率（第7列，索引6）
      if (cells[6]) {
        metrics.conversionRate = this.extractText(cells[6]).trim();

      }

      // 验证是否至少提取到一些指标
      const hasMetrics = Object.values(metrics).some(value => value.length > 0);
      if (!hasMetrics) {
        console.warn('❌ 未能提取到任何指标数据');
        for (let i = 0; i < cells.length; i++) {

        }
      }

    } catch (error) {
      console.error('提取商品指标失败:', error);
    }

    return metrics;
  }

  // 专门处理短视频榜的数据提取
  async extractVideoRankingData(rankingType) {
    try {

      this.showNotification('正在提取短视频榜数据...', 'info');

      // 只清空短视频榜数据，不影响其他榜单
      if (rankingType.category === 'hot_content' && rankingType.type === 'short_video') {

        this.extractedData.hot_content.short_video = [];
      }

      let allExtractedData = [];
      let hasMoreData = true;

      while (hasMoreData && this.isExtracting && this.extractCount < this.maxExtractCount) {


        // 先检查页面是否是表格形式的榜单
        const tableSelectors = [
          '.rank-list-table-body',
          '.rank-list-Table-Body',
          'tbody',
          '.table-body',
          '[role="rowgroup"]'
        ];

        let isTableFormat = false;
        let tableBody = null;

        for (const selector of tableSelectors) {
          tableBody = document.querySelector(selector);
          if (tableBody) {

            isTableFormat = true;
            break;
          }
        }

        let pageData = [];

        if (isTableFormat) {
          // 表格形式短视频榜单处理


          // 获取所有数据行 - 短视频榜特定选择器
          const rowSelectors = [
            '.rank-list-table-row',  // 巨量云图使用的行选择器
            '.rank-list-Table-Row',
            'tr',
            '[role="row"]'
          ];

          let rows = [];
          for (const selector of rowSelectors) {
            const elements = tableBody.querySelectorAll(selector);
            if (elements && elements.length > 0) {

              rows = Array.from(elements);
              break;
            }
          }

          if (rows.length === 0) {
            console.warn('未找到数据行，尝试直接获取tableBody的子元素');
            rows = Array.from(tableBody.children).filter(child =>
              child.classList.contains('rank-list-table-column') ||
              child.classList.contains('rank-list-table-row') ||
              child.tagName === 'TR'
            );
          }



          // 通过表头确定每列的含义 - 短视频榜特定表头
          const headerSelectors = [
            '.rank-list-table-th-title',  // 巨量云图使用的表头标题
            'th',
            '[role="columnheader"]',
            '.rank-list-table-header-cell',
            '.rank-list-Table-HeadCell',
            '.table-header-cell'
          ];

          let headerRow = [];
          for (const selector of headerSelectors) {
            const headers = document.querySelectorAll(selector);
            if (headers && headers.length > 0) {
              headerRow = Array.from(headers);

              break;
            }
          }

          let columnTypes = [];
          if (headerRow && headerRow.length > 0) {
            columnTypes = headerRow.map(header => {
              const text = this.extractText(header).toLowerCase();

              if (text.includes('排名')) return 'rank';
              if (text.includes('视频') || text.includes('内容')) return 'content';
              if (text.includes('关键词') || text.includes('标签')) return 'keywords';
              if (text.includes('曝光') || text.includes('展现')) return 'exposure';
              if (text.includes('完播率')) return 'completeRate';
              if (text.includes('互动率')) return 'interaction';
              if (text.includes('点赞')) return 'likes';
              if (text.includes('评论')) return 'comments';
              if (text.includes('收藏')) return 'favorites';
              if (text.includes('分享')) return 'shares';
              return 'unknown';
            });


          }

          // 处理每一行数据
          for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
            const row = rows[rowIndex];

            if (this.extractCount >= this.maxExtractCount) {

              break;
            }

            // 参考old.js的逻辑 - 直接获取.rank-list-table-cell-body单元格
            const cells = row.querySelectorAll('.rank-list-table-cell-body');
            if (cells.length < 6) {

              continue;
            }



            // 提取排名，如果排名提取失败则使用行索引
            let rank = this.extractRank(cells[0]);
            if (rank === 0) {
              // 对于前三行数据，确保有正确的排名
              if (rowIndex < 3) {
                rank = rowIndex + 1;

              } else {
                rank = rowIndex + 1;

              }
            }

            // 参考old.js的数据结构 - 直接按列索引提取数据
            const videoData = {
              timestamp: Date.now(),
              pageUrl: window.location.href,
              rankingType: rankingType,
              rank: rank,                                        // 使用处理后的排名
              content: this.extractVideoContentFromCell(cells[1]), // 第1列：视频内容
              keywords: this.extractKeywordsFromCell(cells[2]),    // 第2列：关键词
              exposureIndex: this.extractText(cells[3]),           // 第3列：曝光量指数
              completionRate: this.extractText(cells[4]),          // 第4列：完播率指数
              interactionRate: this.extractText(cells[5]),         // 第5列：互动率指数
              // 保持与弹窗展示兼容的字段
              isShortVideo: true,
              type: 'video'
            };

            // 确保前三行的数据被保存，即使排名可能有问题
            if (rowIndex < 3 || videoData.rank > 0) {

              pageData.push(videoData);
            } else {
              console.warn(`第${rowIndex + 1}行数据无效，排名:${videoData.rank}, 标题:${videoData.content?.title}`);
            }
          }
        } else {
          // 尝试查找短视频榜的列表容器（非表格形式）
          const listContainerSelectors = [
            '.video-rank-list',
            '.rank-list-container',
            '[data-e2e="video-rank-list"]',
            '[class*="video"][class*="rank"]',
            '[class*="video"][class*="list"]',
            '.ecom-list',
            '[data-log-module="短视频榜"]',
            '[data-log-value="短视频榜"]',
            '[class*="rankList"]'
          ];

          let listContainer = null;
          for (const selector of listContainerSelectors) {
            const element = document.querySelector(selector);
            if (element) {

              listContainer = element;
              break;
            }
          }

          if (!listContainer) {
            // 如果没有找到专门的容器，尝试获取页面中所有可能的视频项

            listContainer = document;
          }

          // 尝试多种短视频项选择器
          const videoItemSelectors = [
            '.rank-item',
            '.video-item',
            '.video-rank-item',
            '[class*="video-rank-item"]',
            '[class*="videoRankItem"]',
            '.rank-list-item',
            '.video-list-item',
            '[class*="list-item"]',
            '[class*="ListItem"]',
            '[data-log-name="video-item"]'
          ];

          let videoItems = [];
          for (const selector of videoItemSelectors) {
            const elements = listContainer.querySelectorAll(selector);
            if (elements && elements.length > 0) {

              videoItems = Array.from(elements);
              break;
            }
          }

          // 如果还是没找到，尝试查找带有排名的元素
          if (videoItems.length === 0) {

            const rankElements = listContainer.querySelectorAll('[class*="rank"]:not(.rank-list-container):not([class*="header"]):not([class*="title"])');
            if (rankElements.length > 0) {

              videoItems = Array.from(rankElements);
            }
          }

          if (videoItems.length === 0) {
            console.warn('未找到短视频榜数据项');
            break;
          }

          // 处理每个视频项
          for (let i = 0; i < videoItems.length; i++) {
            const item = videoItems[i];
            if (this.extractCount >= this.maxExtractCount) {

              break;
            }

            const videoData = this.extractVideoItemData(item, i);

            // 特殊处理前三个项目，确保它们有正确的排名
            if (i < 3 && (!videoData.rank || videoData.rank === 0)) {
              videoData.rank = i + 1;

            }

            if (videoData) {
              pageData.push(videoData);
            }
          }
        }

        if (pageData.length > 0) {
          // 保存数据
          this.saveExtractedData(rankingType, pageData);
          this.extractCount += pageData.length;
          allExtractedData = [...allExtractedData, ...pageData];


          this.sendProgressUpdate(Math.round((this.extractCount / this.maxExtractCount) * 100), `已提取 ${this.extractCount} 条短视频榜数据`);

          // 检查是否需要翻页
          if (this.extractCount < this.maxExtractCount) {
            hasMoreData = await this.goToNextPage();
            if (hasMoreData) {
              this.currentPage++;
              await this.waitForPageLoad();
              // 新增：翻页后延迟1秒，防止过快
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          } else {
            hasMoreData = false;
          }
        } else {
          console.warn('本页未提取到有效数据，停止获取');
          hasMoreData = false;
        }
      }



      // 记录前三条数据
      const topThree = allExtractedData.filter(item => item.rank <= 3).sort((a, b) => a.rank - b.rank);
      console.log(`前三名数据(${topThree.length}条):`, topThree.map(item => ({
        rank: item.rank,
        title: item.content?.title || item.title
      })));

      return allExtractedData;
    } catch (error) {
      this.showNotification(`短视频榜数据获取失败: ${error.message}`, 'error');
      throw error;
    }
  }

  // 提取短视频榜单项数据
  extractVideoItemData(item, index = -1) {
    try {


      // 创建数据结构，格式必须与弹窗显示期望的格式一致
      const videoData = {
        rank: 0,                    // 排名
        content: {                  // 内容信息
          title: '',                // 标题
          coverUrl: '',             // 封面图
          videoUrl: '',             // 视频链接
          duration: ''              // 视频时长
        },
        stats: {                    // 统计数据
          exposure: '',             // 曝光量
          playRate3s: '',           // 3s完播率
          playRate5s: '',           // 5s完播率
          completeRate: '',         // 完播率
          likes: '',                // 点赞数
          comments: '',             // 评论数
          shares: '',               // 分享数
          favorites: ''             // 收藏数
        },
        author: '',                 // 作者
        keywords: [],               // 关键词
        isShortVideo: true,         // 短视频榜标识
        type: 'video',              // 类型标识
        timestamp: Date.now(),      // 时间戳
        pageUrl: window.location.href // 页面URL
      };

      // 特殊处理 - 通过图片特征检测是否为前三名
      const medalImgs = item.querySelectorAll('img');
      let hasMedalImage = false;

      for (const img of medalImgs) {
        const src = img.src || '';
        const alt = img.alt || '';

        // 检查金牌/第一名特征
        if ((src.includes('gold') || src.includes('medal') || src.includes('first') ||
          alt.includes('第一') || alt.includes('1') || alt.includes('金')) && index <= 2) {
          videoData.rank = 1;
          hasMedalImage = true;

          break;
        }
        // 检查银牌/第二名特征
        else if ((src.includes('silver') || src.includes('second') ||
          alt.includes('第二') || alt.includes('2') || alt.includes('银')) && index <= 2) {
          videoData.rank = 2;
          hasMedalImage = true;

          break;
        }
        // 检查铜牌/第三名特征
        else if ((src.includes('bronze') || src.includes('third') ||
          alt.includes('第三') || alt.includes('3') || alt.includes('铜')) && index <= 2) {
          videoData.rank = 3;
          hasMedalImage = true;

          break;
        }
      }

      // 如果没有通过奖牌图片识别，则尝试其他方法提取排名
      if (!hasMedalImage) {
        // 检查是否有排名图标元素
        const rankIcons = item.querySelectorAll('[class*="rank-icon"], [class*="rankIcon"], [class*="medal"]');

        if (rankIcons.length > 0) {
          for (const icon of rankIcons) {
            // 检查特定特征识别前三名
            const classList = Array.from(icon.classList);
            const hasRank1 = classList.some(cls => cls.includes('rank-1') || cls.includes('first') || cls.includes('top-1'));
            const hasRank2 = classList.some(cls => cls.includes('rank-2') || cls.includes('second') || cls.includes('top-2'));
            const hasRank3 = classList.some(cls => cls.includes('rank-3') || cls.includes('third') || cls.includes('top-3'));

            if (hasRank1) {
              videoData.rank = 1;

              hasMedalImage = true;
              break;
            } else if (hasRank2) {
              videoData.rank = 2;

              hasMedalImage = true;
              break;
            } else if (hasRank3) {
              videoData.rank = 3;

              hasMedalImage = true;
              break;
            }
          }
        }
      }

      // 提取排名 - 多种可能的选择器
      if (!hasMedalImage) {
        const rankSelectors = [
          '[class*="rank-num"]',
          '[class*="rankNum"]',
          '[class*="rank-index"]',
          '[class*="rankIndex"]',
          '.num',
          '.rank',
          '.index',
          'span:first-child'
        ];

        for (const selector of rankSelectors) {
          const rankElement = item.querySelector(selector);
          if (rankElement) {
            const rankText = this.extractText(rankElement);
            if (rankText) {
              // 尝试提取数字
              const rankMatch = rankText.match(/\d+/);
              if (rankMatch) {
                videoData.rank = parseInt(rankMatch[0]);

                break;
              }
            }
          }
        }
      }

      // 如果还是无法获取排名但有提供index，使用index+1作为排名
      if (videoData.rank === 0 && index >= 0) {
        videoData.rank = index + 1;

      }

      // 对于前三个项，强制设置正确的排名
      if (index >= 0 && index < 3 && videoData.rank === 0) {
        videoData.rank = index + 1;

      }

      // 提取标题
      const titleSelectors = [
        '[class*="title"]',
        '[class*="Title"]',
        '[class*="content"]',
        '[class*="Content"]',
        '[class*="desc"]',
        '[class*="Desc"]',
        '[style*="display: -webkit-box"]',
        'div[style*="overflow: hidden"]',
        'span[style*="overflow: hidden"]'
      ];

      for (const selector of titleSelectors) {
        const titleElement = item.querySelector(selector);
        if (titleElement && titleElement.textContent.trim()) {
          videoData.content.title = this.extractText(titleElement);

          break;
        }
      }

      // 提取视频时长
      const durationSelectors = [
        '[class*="duration"]',
        '[class*="Duration"]',
        '[class*="time"]',
        '[class*="Time"]'
      ];

      for (const selector of durationSelectors) {
        const durationElement = item.querySelector(selector);
        if (durationElement && durationElement.textContent.trim()) {
          videoData.content.duration = this.extractText(durationElement);

          break;
        }
      }

      // 提取作者/创作者
      const authorSelectors = [
        '[class*="author"]',
        '[class*="Author"]',
        '[class*="creator"]',
      ];

      for (const selector of authorSelectors) {
        const authorElement = item.querySelector(selector);
        if (authorElement && authorElement.textContent.trim()) {
          videoData.author = this.extractText(authorElement);

          break;
        }
      }

      // 提取关键词
      const keywordSelectors = [
        '[class*="keyword"]',
        '[class*="Keyword"]',
        '[class*="tag"]',
        '[class*="Tag"]'
      ];

      for (const selector of keywordSelectors) {
        const keywordElements = item.querySelectorAll(selector);
        if (keywordElements.length > 0) {
          videoData.keywords = Array.from(keywordElements).map(el => this.extractText(el));

          break;
        }
      }

      // 提取曝光量
      const exposureSelectors = [
        '[class*="exposure"]',
        '[class*="Exposure"]',
        '[class*="impression"]',
        '[class*="Impression"]'
      ];

      for (const selector of exposureSelectors) {
        const exposureElement = item.querySelector(selector);
        if (exposureElement && exposureElement.textContent.trim()) {
          videoData.stats.exposure = this.extractText(exposureElement);

          break;
        }
      }

      // 提取3s完播率
      const playRate3sSelectors = [
        '[class*="playRate3s"]',
        '[class*="PlayRate3s"]',
        '[class*="play-rate-3s"]',
        '[class*="Play-rate-3s"]'
      ];

      for (const selector of playRate3sSelectors) {
        const playRate3sElement = item.querySelector(selector);
        if (playRate3sElement && playRate3sElement.textContent.trim()) {
          videoData.stats.playRate3s = this.extractText(playRate3sElement);

          break;
        }
      }

      // 提取5s完播率
      const playRate5sSelectors = [
        '[class*="playRate5s"]',
        '[class*="PlayRate5s"]',
        '[class*="play-rate-5s"]',
        '[class*="Play-rate-5s"]'
      ];

      for (const selector of playRate5sSelectors) {
        const playRate5sElement = item.querySelector(selector);
        if (playRate5sElement && playRate5sElement.textContent.trim()) {
          videoData.stats.playRate5s = this.extractText(playRate5sElement);

          break;
        }
      }

      // 提取完播率
      const completeRateSelectors = [
        '[class*="completeRate"]',
        '[class*="CompleteRate"]',
        '[class*="completionRate"]',
        '[class*="CompletionRate"]'
      ];

      for (const selector of completeRateSelectors) {
        const completeRateElement = item.querySelector(selector);
        if (completeRateElement && completeRateElement.textContent.trim()) {
          videoData.stats.completeRate = this.extractText(completeRateElement);

          break;
        }
      }

      // 提取点赞数
      const likesSelectors = [
        '[class*="likes"]',
        '[class*="Likes"]',
        '[class*="favorites"]',
        '[class*="Favorites"]'
      ];

      for (const selector of likesSelectors) {
        const likesElement = item.querySelector(selector);
        if (likesElement && likesElement.textContent.trim()) {
          const likesText = this.extractText(likesElement);
          videoData.stats.likes = typeof this.parseNumber === 'function'
            ? this.parseNumber(likesText)
            : parseInt(likesText) || 0;

          break;
        }
      }

      // 提取评论数
      const commentsSelectors = [
        '[class*="comments"]',
        '[class*="Comments"]',
        '[class*="comment"]',
        '[class*="Comment"]'
      ];

      for (const selector of commentsSelectors) {
        const commentsElement = item.querySelector(selector);
        if (commentsElement && commentsElement.textContent.trim()) {
          const commentsText = this.extractText(commentsElement);
          videoData.stats.comments = typeof this.parseNumber === 'function'
            ? this.parseNumber(commentsText)
            : parseInt(commentsText) || 0;

          break;
        }
      }

      // 提取分享数
      const sharesSelectors = [
        '[class*="shares"]',
        '[class*="Shares"]',
        '[class*="share"]',
        '[class*="Share"]'
      ];

      for (const selector of sharesSelectors) {
        const sharesElement = item.querySelector(selector);
        if (sharesElement && sharesElement.textContent.trim()) {
          const sharesText = this.extractText(sharesElement);
          videoData.stats.shares = typeof this.parseNumber === 'function'
            ? this.parseNumber(sharesText)
            : parseInt(sharesText) || 0;

          break;
        }
      }

      // 提取收藏数
      const favoritesSelectors = [
        '[class*="favorites"]',
        '[class*="Favorites"]',
        '[class*="favorite"]',
        '[class*="Favorite"]'
      ];

      for (const selector of favoritesSelectors) {
        const favoritesElement = item.querySelector(selector);
        if (favoritesElement && favoritesElement.textContent.trim()) {
          const favoritesText = this.extractText(favoritesElement);
          videoData.stats.favorites = typeof this.parseNumber === 'function'
            ? this.parseNumber(favoritesText)
            : parseInt(favoritesText) || 0;

          break;
        }
      }

      // 提取封面图
      const coverSelectors = [
        'img',
        '[class*="cover"] img',
        '[class*="Cover"] img',
        '[class*="thumbnail"] img',
        '[class*="Thumbnail"] img',
        '[class*="image"] img',
        '[class*="Image"] img'
      ];

      for (const selector of coverSelectors) {
        const coverElement = item.querySelector(selector);
        if (coverElement && coverElement.src) {
          videoData.content.coverUrl = coverElement.src;

          break;
        }
      }

      // 提取视频链接 - 可能在父元素或子元素的链接中
      const linkSelectors = [
        'a',
        '[class*="link"]',
        '[class*="Link"]',
        '[href]'
      ];

      // 先检查item本身是否是链接
      if (item.tagName === 'A' && item.href) {
        videoData.content.videoUrl = item.href;

      } else {
        // 检查子元素
        for (const selector of linkSelectors) {
          const linkElement = item.querySelector(selector);
          if (linkElement && linkElement.href) {
            videoData.content.videoUrl = linkElement.href;

            break;
          }
        }

        // 如果没找到，检查父元素
        if (!videoData.content.videoUrl) {
          let parent = item.parentElement;
          let maxDepth = 3; // 限制向上查找的层数

          while (parent && maxDepth > 0) {
            if (parent.tagName === 'A' && parent.href) {
              videoData.content.videoUrl = parent.href;

              break;
            }
            parent = parent.parentElement;
            maxDepth--;
          }
        }
      }

      // 验证数据是否有效 - 至少需要排名和标题
      if (videoData.rank > 0 && videoData.content.title) {
        return videoData;
      }

      console.warn('提取的短视频数据不完整:', videoData);
      return null;
    } catch (error) {
      console.error('提取短视频榜单项数据失败:', error);
      return null;
    }
  }

  // ==================== 待开发的获取方法 ====================

  // 商品卡榜数据获取 - 支持自动翻页
  async extractProductCardRankingData(rankingType) {
    try {

      this.showNotification('正在获取商品卡榜数据...', 'info');

      // 重置页码和获取状态
      this.currentPage = 1;

      const extractedProducts = [];
      let currentPage = 1;
      let hasNextPage = true;
      const maxPages = 10; // 最大页数限制
      const maxItems = this.maxExtractCount || 100; // 使用类属性的最大获取数量

      while (hasNextPage && currentPage <= maxPages && extractedProducts.length < maxItems && this.isExtracting) {


        // 同步更新类属性的页码
        this.currentPage = currentPage;

        // 发送进度更新 - 使用标准格式
        const progress = Math.round((extractedProducts.length / maxItems) * 100);
        this.sendProgressUpdate(progress, `正在获取第 ${currentPage} 页商品卡榜数据... (已获取 ${extractedProducts.length} 个商品)`);

        // 等待页面加载完成
        await this.waitForPageLoad();

        // 提取当前页面的商品数据
        const currentPageProducts = await this.extractCurrentPageProductCardData(rankingType);

        if (currentPageProducts.length > 0) {
          // 添加页码信息和全局排名
          currentPageProducts.forEach((product, index) => {
            product.pageNumber = currentPage;
            product.globalRank = extractedProducts.length + index + 1;
            // 保持原有的页面内排名作为 pageRank
            product.pageRank = product.rank;
            // 更新为全局排名
            product.rank = product.globalRank;
            extractedProducts.push(product);
          });

          // 更新类属性的获取计数
          this.extractCount = extractedProducts.length;



        } else {
          console.warn(`❌ 第 ${currentPage} 页未提取到商品数据`);
        }

        // 检查是否达到最大数量
        if (extractedProducts.length >= maxItems) {

          break;
        }

        // 尝试翻到下一页
        hasNextPage = await this.goToNextPage();
        if (hasNextPage) {
          currentPage++;

          // 等待页面跳转和加载
          await this.waitForPageLoad();
          // 新增：翻页后延迟1秒，防止过快
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {

        }
      }




      // 更新最终的获取计数
      this.extractCount = extractedProducts.length;

      // 保存数据
      this.saveExtractedData(rankingType, extractedProducts);

      // 发送完成通知 - 使用标准格式
      this.sendProgressUpdate(100, `商品卡榜获取完成，共 ${extractedProducts.length} 个商品 (${currentPage} 页)`);

      return extractedProducts;

    } catch (error) {
      this.sendErrorUpdate(`商品卡榜数据获取失败: ${error.message}`);
      return [];
    }
  }

  // 提取当前页面的商品卡数据
  async extractCurrentPageProductCardData(rankingType) {
    try {


      // 查找商品卡榜表格
      const tableSelectors = [
        'table[role="table"]',
        '.rank-list-Table-Implement',
        '.rank-list-Table',
        'table',
        '.RankListTableContainer__Content-iKxfFA table',
        '[class*="rank-list-Table"]'
      ];

      let table = null;
      for (const selector of tableSelectors) {
        const found = document.querySelector(selector);
        if (found) {
          table = found;

          break;
        }
      }

      if (!table) {
        // 尝试通过标题查找
        const titleElements = document.querySelectorAll('[data-log-value="商品卡榜"], .title');
        for (const titleEl of titleElements) {
          const container = titleEl.closest('[class*="Container"], [class*="Table"]');
          if (container) {
            const tableInContainer = container.querySelector('table');
            if (tableInContainer) {
              table = tableInContainer;

              break;
            }
          }
        }

        if (!table) {
          throw new Error('未找到商品卡榜表格');
        }
      }

      // 查找数据行
      const rowSelectors = [
        'tbody tr[role="row"]',
        'tbody .rank-list-Table-Row',
        '.rank-list-Table-Body .rank-list-Table-Row',
        'tr[role="row"][aria-rowindex]',
        'tr[aria-rowindex]:not([aria-rowindex="1"])',
        'tbody tr',
        'tr:not(:first-child)'
      ];

      let rows = [];
      for (const selector of rowSelectors) {
        const found = Array.from(table.querySelectorAll(selector));
        if (found.length > 0) {
          rows = found;

          break;
        }
      }

      // 如果没找到，尝试直接从tbody获取
      if (rows.length === 0) {
        const tbody = table.querySelector('tbody');
        if (tbody) {
          rows = Array.from(tbody.children);

        } else {
          const allTableRows = Array.from(table.querySelectorAll('tr, [role="row"]'));
          if (allTableRows.length > 1) {
            rows = allTableRows.slice(1);

          }
        }
      }

      if (rows.length === 0) {
        console.warn('当前页面未找到商品数据行');
        return [];
      }

      // 提取每行的商品数据
      const currentPageProducts = [];
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];


        try {
          // 不传递全局排名，让方法内部从表格中提取
          const productData = this.extractProductCardRowData(row, i + 1);
          if (productData && productData.product && productData.product.title) {
            currentPageProducts.push(productData);

          }
        } catch (error) {
          console.error(`提取第 ${i + 1} 行商品数据失败:`, error);
        }
      }


      return currentPageProducts;

    } catch (error) {
      console.error('提取当前页面商品卡数据失败:', error);
      return [];
    }
  }

  // 提取商品卡行数据
  extractProductCardRowData(row, index, globalRank = null) {
    try {



      // 获取所有单元格
      const cells = Array.from(row.querySelectorAll('.rank-list-Table-Cell, td, [role="cell"]'));


      if (cells.length < 7) {
        console.warn(`单元格数量不足 (${cells.length} < 7)，可能数据不完整`);
      }

      // 提取排名（第1列）
      let rank = globalRank || index; // 优先使用全局排名
      if (cells[0]) {
        const rankText = this.extractText(cells[0]);
        const rankMatch = rankText.match(/(\d+)/);
        if (rankMatch) {
          const localRank = parseInt(rankMatch[1]);
          // 如果没有提供全局排名，使用本地排名
          if (!globalRank) {
            rank = localRank;
          }

        }
      }


      // 提取商品信息（第2列）
      const productInfo = this.extractProductInfo(cells[1]);


      // 提取各项指标数据
      const metrics = this.extractProductMetrics(cells);


      // 构建商品数据对象
      const productData = {
        rank: rank,
        timestamp: Date.now(),
        pageUrl: window.location.href,
        product: productInfo,
        metrics: metrics,
        isProductCard: true,
        extractSource: 'oceanengine_product_card'
      };


      return productData;

    } catch (error) {
      console.error('提取商品卡行数据失败:', error);
      return null;
    }
  }

  // 提取商品信息
  extractProductInfo(cell) {
    const productInfo = {
      title: '',
      id: '',
      image: '',
      url: ''
    };

    if (!cell) {
      console.warn('商品信息单元格为空');
      return productInfo;
    }

    try {
      // 提取商品标题
      const titleSelectors = [
        '.ellipsisText__EllipsisTextItemContainer-eQpLFK.ebmtII',
        '.ProductCell__NameContainer-eJazdt span',
        '[style*="color: rgb(31, 75, 217)"]',
        '.product-title',
        '.item-title'
      ];

      for (const selector of titleSelectors) {
        const titleElement = cell.querySelector(selector);
        if (titleElement) {
          productInfo.title = this.extractText(titleElement).trim();

          break;
        }
      }

      // 提取商品ID
      const idSelectors = [
        '.id span',
        '.product-id',
        '[style*="text-align: left"]'
      ];

      for (const selector of idSelectors) {
        const idElement = cell.querySelector(selector);
        if (idElement) {
          const idText = this.extractText(idElement);
          const idMatch = idText.match(/ID[:\s]*(\d+)/i);
          if (idMatch) {
            productInfo.id = idMatch[1];

            break;
          }
        }
      }

      // 提取商品图片
      const imageSelectors = [
        '.Imgs__ImgContainer-bIvTs img',
        '.product-image img',
        'img[src*="ecombdimg.com"]',
        'img'
      ];

      for (const selector of imageSelectors) {
        const imgElement = cell.querySelector(selector);
        if (imgElement && imgElement.src) {
          productInfo.image = imgElement.src;

          break;
        }
      }

      // 尝试从全部文本中提取商品ID（如果前面没有找到）
      if (!productInfo.id) {
        const allText = this.extractText(cell);
        const idMatch = allText.match(/ID[:\s]*(\d+)/i);
        if (idMatch) {
          productInfo.id = idMatch[1];

        }
      }

      // 如果没有标题，尝试从全部文本中提取
      if (!productInfo.title) {
        const allTextElement = cell.querySelector('.ProductCell__TitleContainer-jJdGNm, .product-info, .title');
        if (allTextElement) {
          const fullText = this.extractText(allTextElement);
          // 提取第一行作为标题（通常ID在第二行）
          const lines = fullText.split('\n').filter(line => line.trim());
          if (lines.length > 0) {
            productInfo.title = lines[0].trim();

          }
        }
      }

    } catch (error) {
      console.error('提取商品信息失败:', error);
    }

    return productInfo;
  }

  // 提取商品指标数据
  extractProductMetrics(cells) {
    const metrics = {
      gmvIndex: '',      // GMV指数
      price: '',         // 商品售卖价
      salesIndex: '',    // 销售量指数  
      clickIndex: '',    // 点击指数
      conversionRate: '' // 转化率
    };

    try {
      // GMV指数（第3列）
      if (cells[2]) {
        metrics.gmvIndex = this.extractText(cells[2]).trim();

      }

      // 商品售卖价（第4列）
      if (cells[3]) {
        const priceText = this.extractText(cells[3]).trim();
        metrics.price = priceText;

      }

      // 销售量指数（第5列）
      if (cells[4]) {
        metrics.salesIndex = this.extractText(cells[4]).trim();

      }

      // 点击指数（第6列）
      if (cells[5]) {
        metrics.clickIndex = this.extractText(cells[5]).trim();

      }

      // 转化率（第7列）
      if (cells[6]) {
        metrics.conversionRate = this.extractText(cells[6]).trim();

      }

    } catch (error) {
      console.error('提取商品指标失败:', error);
    }

    return metrics;
  }

  // 电商行业榜数据获取 - 支持自动翻页
  async extractRetailersRankingData(rankingType) {
    try {

      this.showNotification('正在获取电商行业榜数据...', 'info');

      // 重置页码和获取状态
      this.currentPage = 1;

      const extractedInfluencers = [];
      let currentPage = 1;
      let hasNextPage = true;
      const maxPages = 10; // 最大页数限制
      const maxItems = this.maxExtractCount || 100; // 使用类属性的最大获取数量

      while (hasNextPage && currentPage <= maxPages && extractedInfluencers.length < maxItems && this.isExtracting) {


        // 同步更新类属性的页码
        this.currentPage = currentPage;

        // 发送进度更新 - 使用标准格式
        const progress = Math.round((extractedInfluencers.length / maxItems) * 100);
        this.sendProgressUpdate(progress, `正在获取第 ${currentPage} 页电商行业榜数据... (已获取 ${extractedInfluencers.length} 个达人)`);

        // 等待页面加载完成
        await this.waitForPageLoad();

        // 提取当前页面的达人数据
        const currentPageInfluencers = await this.extractCurrentPageRetailersData(rankingType);

        if (currentPageInfluencers.length > 0) {
          // 添加页码信息和全局排名
          currentPageInfluencers.forEach((influencer, index) => {
            influencer.pageNumber = currentPage;
            influencer.globalRank = extractedInfluencers.length + index + 1;
            // 保持原有的页面内排名作为 pageRank
            influencer.pageRank = influencer.rank;
            // 更新为全局排名
            influencer.rank = influencer.globalRank;
            extractedInfluencers.push(influencer);
          });

          // 更新类属性的获取计数
          this.extractCount = extractedInfluencers.length;



        } else {
          console.warn(`❌ 第 ${currentPage} 页未提取到达人数据`);
        }

        // 检查是否达到最大数量
        if (extractedInfluencers.length >= maxItems) {

          break;
        }

        // 尝试翻到下一页
        hasNextPage = await this.goToNextPage();
        if (hasNextPage) {
          currentPage++;

          // 等待页面跳转和加载
          await this.waitForPageLoad();
          // 新增：翻页后延迟1秒，防止过快
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {

        }
      }




      // 更新最终的获取计数
      this.extractCount = extractedInfluencers.length;

      // 保存数据
      this.saveExtractedData(rankingType, extractedInfluencers);

      // 发送完成通知 - 使用标准格式
      this.sendProgressUpdate(100, `电商行业榜获取完成，共 ${extractedInfluencers.length} 个达人 (${currentPage} 页)`);

      return extractedInfluencers;

    } catch (error) {
      this.sendErrorUpdate(`电商行业榜数据获取失败: ${error.message}`);
      return [];
    }
  }

  // 提取当前页面的电商行业榜数据
  async extractCurrentPageRetailersData(rankingType) {
    try {


      // 查找电商行业榜表格
      const tableSelectors = [
        '.rank-list-table-body',
        '.rank-list-Table-Body',
        'table[role="table"] tbody',
        '.rank-list-table tbody',
        'tbody'
      ];

      let tableBody = null;
      for (const selector of tableSelectors) {
        const found = document.querySelector(selector);
        if (found) {
          tableBody = found;

          break;
        }
      }

      if (!tableBody) {
        throw new Error('未找到电商行业榜表格');
      }

      // 查找数据行
      const rowSelectors = [
        '.rank-list-table-column.rank-list-table-row',
        '.rank-list-Table-Row',
        'tr[role="row"]',
        'tr:not(:first-child)',
        '.rank-list-table-column'
      ];

      let rows = [];
      for (const selector of rowSelectors) {
        const found = Array.from(tableBody.querySelectorAll(selector));
        if (found.length > 0) {
          rows = found;

          break;
        }
      }

      // 如果没找到，尝试直接从tableBody获取
      if (rows.length === 0) {
        rows = Array.from(tableBody.children);

      }

      if (rows.length === 0) {
        console.warn('当前页面未找到达人数据行');
        return [];
      }

      // 提取每行的达人数据
      const currentPageInfluencers = [];
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];


        try {
          const influencerData = this.extractRetailersRowData(row, i + 1);
          if (influencerData && influencerData.influencer && influencerData.influencer.name) {
            currentPageInfluencers.push(influencerData);

          }
        } catch (error) {
          console.error(`提取第 ${i + 1} 行达人数据失败:`, error);
        }
      }


      return currentPageInfluencers;

    } catch (error) {
      console.error('提取当前页面电商行业榜数据失败:', error);
      return [];
    }
  }

  // 提取电商行业榜行数据
  extractRetailersRowData(row, index, globalRank = null) {
    try {



      // 获取所有单元格
      const cells = Array.from(row.querySelectorAll('.rank-list-table-cell, td, [role="cell"]'));


      if (cells.length < 8) {
        console.warn(`单元格数量不足 (${cells.length} < 8)，可能数据不完整`);
      }

      // 提取排名（第1列）
      let rank = globalRank || index; // 优先使用全局排名
      if (cells[0]) {
        const rankData = this.extractInfluencerRank(cells[0]);
        if (rankData.rank > 0) {
          rank = globalRank || rankData.rank;
        }

      }


      // 提取达人信息（第2列）
      const influencerInfo = this.extractInfluencerInfo(cells[1]);


      // 提取主推类目（第3列）
      const categories = this.extractInfluencerCategories(cells[2]);


      // 提取各项指标数据
      const metrics = this.extractInfluencerMetrics(cells);


      // 构建达人数据对象
      const influencerData = {
        rank: rank,
        timestamp: Date.now(),
        pageUrl: window.location.href,
        influencer: influencerInfo,
        categories: categories,
        metrics: metrics,
        isRetailers: true,
        extractSource: 'oceanengine_retailers_ranking'
      };


      return influencerData;

    } catch (error) {
      console.error('提取电商行业榜行数据失败:', error);
      return null;
    }
  }

  // 提取达人排名信息
  extractInfluencerRank(cell) {
    try {
      // 检查是否有排名图片（前三名通常有特殊图标）
      const rankImg = cell.querySelector('img');
      if (rankImg && rankImg.src) {
        const src = rankImg.src;

        // 通过图片识别前三名
        if (src.includes('FEB332') || src.includes('gold') || src.includes('rank1')) {

          return { rank: 1, hasIcon: true };
        } else if (src.includes('C6CBD1') || src.includes('silver') || src.includes('rank2')) {

          return { rank: 2, hasIcon: true };
        } else if (src.includes('BF7C1F') || src.includes('bronze') || src.includes('rank3')) {

          return { rank: 3, hasIcon: true };
        }
      }

      // 检查文本排名
      const rankElement = cell.querySelector('.useTableColumnsComponents__Rank-clKjlu, [class*="Rank"]');
      if (rankElement) {
        const rankText = this.extractText(rankElement);
        const rankNum = parseInt(rankText);
        if (rankNum > 0) {

          return { rank: rankNum, hasIcon: false };
        }
      }

      // 尝试从整个单元格文本提取
      const cellText = this.extractText(cell);
      const rankMatch = cellText.match(/(\d+)/);
      if (rankMatch) {
        const rankNum = parseInt(rankMatch[1]);

        return { rank: rankNum, hasIcon: false };
      }


      return { rank: 0, hasIcon: false };
    } catch (error) {
      return { rank: 0, hasIcon: false };
    }
  }

  // 提取达人信息
  extractInfluencerInfo(cell) {
    const influencerInfo = {
      name: '',
      avatar: '',
      fansCount: '',
      fansCountNum: 0
    };

    if (!cell) {
      console.warn('达人信息单元格为空');
      return influencerInfo;
    }

    try {
      // 提取达人头像
      const avatarSelectors = [
        '.useTableColumnsComponents__StarBox-dJlgxR img.avatar',
        '.avatar',
        'img[src*="douyinpic.com"]',
        'img[src*="avatar"]',
        'img'
      ];

      for (const selector of avatarSelectors) {
        const avatarElement = cell.querySelector(selector);
        if (avatarElement && avatarElement.src) {
          influencerInfo.avatar = avatarElement.src;

          break;
        }
      }

      // 提取达人昵称
      const nameSelectors = [
        '.useTableColumnsComponents__StarBox-dJlgxR .info .name div',
        '.useTableColumnsComponents__Ellipsis-cNHHLp',
        '.info .name div',
        '.name div',
        '.info .name'
      ];

      for (const selector of nameSelectors) {
        const nameElement = cell.querySelector(selector);
        if (nameElement) {
          influencerInfo.name = this.extractText(nameElement).trim();
          if (influencerInfo.name) {

            break;
          }
        }
      }

      // 提取粉丝数
      const fansSelectors = [
        '.useTableColumnsComponents__StarBox-dJlgxR .info .fans .num',
        '.fans .num',
        '.num'
      ];

      for (const selector of fansSelectors) {
        const fansElement = cell.querySelector(selector);
        if (fansElement) {
          influencerInfo.fansCount = this.extractText(fansElement).trim();
          // 转换为数字（处理万、千等单位）
          if (influencerInfo.fansCount) {
            influencerInfo.fansCountNum = this.parseNumber(influencerInfo.fansCount);

            break;
          }
        }
      }

      // 如果没有找到昵称，尝试从整个信息区域提取
      if (!influencerInfo.name) {
        const infoElement = cell.querySelector('.info, .useTableColumnsComponents__StarBox-dJlgxR');
        if (infoElement) {
          const allText = this.extractText(infoElement);
          const lines = allText.split('\n').filter(line => line.trim());
          // 第一行通常是昵称
          if (lines.length > 0 && !lines[0].includes('粉丝数')) {
            influencerInfo.name = lines[0].trim();

          }
        }
      }

    } catch (error) {
      console.error('提取达人信息失败:', error);
    }

    return influencerInfo;
  }

  // 提取达人主推类目
  extractInfluencerCategories(cell) {
    const categories = [];

    if (!cell) {
      console.warn('主推类目单元格为空');
      return categories;
    }

    try {
      const categoryText = this.extractText(cell).trim();
      if (categoryText) {
        // 按逗号分割类目
        const categoryList = categoryText.split(',').map(cat => cat.trim()).filter(cat => cat);
        categories.push(...categoryList);

      }
    } catch (error) {
      console.error('提取主推类目失败:', error);
    }

    return categories;
  }

  // 提取达人指标数据
  extractInfluencerMetrics(cells) {
    const metrics = {
      salesAmount: '',         // 销售金额（第4列）
      avgViewers: '',          // 场均观看人数（第5列）
      avgWatchTime: '',        // 人均看播时长（第6列）
      interactionRate: '',     // 互动率（第7列）
      reputation: ''           // 带货口碑（第8列）
    };

    try {
      // 销售金额（第4列，索引3）
      if (cells[3]) {
        metrics.salesAmount = this.extractText(cells[3]).trim();

      }

      // 场均观看人数（第5列，索引4）
      if (cells[4]) {
        metrics.avgViewers = this.extractText(cells[4]).trim();

      }

      // 人均看播时长（第6列，索引5）
      if (cells[5]) {
        metrics.avgWatchTime = this.extractText(cells[5]).trim();

      }

      // 互动率（第7列，索引6）
      if (cells[6]) {
        metrics.interactionRate = this.extractText(cells[6]).trim();

      }

      // 带货口碑（第8列，索引7）
      if (cells[7]) {
        metrics.reputation = this.extractText(cells[7]).trim();

      }

      // 验证是否至少提取到一些指标
      const hasMetrics = Object.values(metrics).some(value => value.length > 0);
      if (!hasMetrics) {
        console.warn('❌ 未能提取到任何指标数据');
        for (let i = 0; i < cells.length; i++) {

        }
      }

    } catch (error) {
      console.error('提取达人指标失败:', error);
    }

    return metrics;
  }

  // 应用下载榜数据获取 - 支持自动翻页
  async extractAppRankingData(rankingType) {
    try {

      this.showNotification('正在获取应用下载榜数据...', 'info');

      // 重置页码和获取状态
      this.currentPage = 1;

      const extractedInfluencers = [];
      let currentPage = 1;
      let hasNextPage = true;
      const maxPages = 10; // 最大页数限制
      const maxItems = this.maxExtractCount || 100; // 使用类属性的最大获取数量

      while (hasNextPage && currentPage <= maxPages && extractedInfluencers.length < maxItems && this.isExtracting) {


        // 同步更新类属性的页码
        this.currentPage = currentPage;

        // 发送进度更新 - 使用标准格式
        const progress = Math.round((extractedInfluencers.length / maxItems) * 100);
        this.sendProgressUpdate(progress, `正在获取第 ${currentPage} 页应用下载榜数据... (已获取 ${extractedInfluencers.length} 个达人)`);

        // 等待页面加载完成
        await this.waitForPageLoad();

        // 提取当前页面的达人数据
        const currentPageInfluencers = await this.extractCurrentPageAppRankingData(rankingType);

        if (currentPageInfluencers.length > 0) {
          // 添加页码信息和全局排名
          currentPageInfluencers.forEach((influencer, index) => {
            influencer.pageNumber = currentPage;
            influencer.globalRank = extractedInfluencers.length + index + 1;
            // 保持原有的页面内排名作为 pageRank
            influencer.pageRank = influencer.rank;
            // 更新为全局排名
            influencer.rank = influencer.globalRank;
            extractedInfluencers.push(influencer);
          });

          // 更新类属性的获取计数
          this.extractCount = extractedInfluencers.length;



        } else {
          console.warn(`❌ 第 ${currentPage} 页未提取到达人数据`);
        }

        // 检查是否达到最大数量
        if (extractedInfluencers.length >= maxItems) {

          break;
        }

        // 尝试翻到下一页
        hasNextPage = await this.goToNextPage();
        if (hasNextPage) {
          currentPage++;

          // 等待页面跳转和加载
          await this.waitForPageLoad();
          // 新增：翻页后延迟1秒，防止过快
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {

        }
      }




      // 更新最终的获取计数
      this.extractCount = extractedInfluencers.length;

      // 保存数据
      this.saveExtractedData(rankingType, extractedInfluencers);

      // 发送完成通知 - 使用标准格式
      this.sendProgressUpdate(100, `应用下载榜获取完成，共 ${extractedInfluencers.length} 个达人 (${currentPage} 页)`);

      return extractedInfluencers;

    } catch (error) {
      this.sendErrorUpdate(`应用下载榜数据获取失败: ${error.message}`);
      return [];
    }
  }

  // 提取当前页面的应用下载榜数据
  async extractCurrentPageAppRankingData(rankingType) {
    try {


      // 查找应用下载榜表格主体
      const tableSelectors = [
        '.rank-list-table-body',
        '.rank-list-Table-Body',
        'table[role="table"] tbody',
        '.rank-list-table tbody',
        'tbody'
      ];

      let tableBody = null;
      for (const selector of tableSelectors) {
        const found = document.querySelector(selector);
        if (found) {
          tableBody = found;

          break;
        }
      }

      if (!tableBody) {
        console.error('未找到应用下载榜表格主体');
        // 尝试查找整个表格容器
        const containerSelectors = [
          '.rank-list-table',
          '.RankListTableContainer__Content-iKxfFA',
          '.rank-list-table-container'
        ];
        for (const selector of containerSelectors) {
          const found = document.querySelector(selector);
          if (found) {
            tableBody = found;

            break;
          }
        }
      }

      if (!tableBody) {
        throw new Error('未找到应用下载榜表格');
      }

      // 查找数据行 - 应用下载榜的行结构
      const rowSelectors = [
        '.rank-list-table-column.rank-list-table-row',
        '.rank-list-table-row',
        '.rank-list-Table-Row',
        'tr[role="row"]',
        'tr:not(:first-child)',
        '.rank-list-table-column'
      ];

      let rows = [];
      for (const selector of rowSelectors) {
        const found = Array.from(tableBody.querySelectorAll(selector));
        if (found.length > 0) {
          rows = found;

          break;
        }
      }

      // 如果没找到，尝试直接从tableBody获取子元素
      if (rows.length === 0) {
        const children = Array.from(tableBody.children);

        // 过滤掉表头等非数据行
        rows = children.filter(child => {
          const hasRankCell = child.querySelector('.rank-list-table-cell');
          const hasDataContent = child.querySelector('.useTableColumnsComponents__StarBox-dJlgxR, .avatar, [src*="douyinpic.com"]');
          return hasRankCell && hasDataContent;
        });

      }

      if (rows.length === 0) {
        console.warn('当前页面未找到达人数据行');
        // 输出调试信息

        return [];
      }

      // 提取每行的达人数据
      const currentPageInfluencers = [];
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];


        try {
          const influencerData = this.extractAppRankingRowData(row, i + 1);
          if (influencerData && influencerData.influencer && influencerData.influencer.name) {
            currentPageInfluencers.push(influencerData);

          } else {
            console.warn(`第 ${i + 1} 行数据无效:`, influencerData);
          }
        } catch (error) {
          console.error(`提取第 ${i + 1} 行达人数据失败:`, error);
          // 输出该行的HTML以便调试

        }
      }


      return currentPageInfluencers;

    } catch (error) {
      console.error('提取当前页面应用下载榜数据失败:', error);
      return [];
    }
  }

  // 提取应用下载榜行数据
  extractAppRankingRowData(row, index, globalRank = null) {
    try {



      // 获取所有单元格
      const cells = Array.from(row.querySelectorAll('.rank-list-table-cell, td, [role="cell"]'));


      if (cells.length < 5) {
        console.warn(`单元格数量不足 (${cells.length} < 5)，可能数据不完整`);
      }

      // 提取排名（第1列）
      let rank = globalRank || index; // 优先使用全局排名
      if (cells[0]) {
        const rankData = this.extractInfluencerRank(cells[0]);
        if (rankData.rank > 0) {
          rank = globalRank || rankData.rank;
        }

      }


      // 提取达人信息（第2列）- 使用应用下载榜专门方法
      const influencerInfo = this.extractAppInfluencerInfo(cells[1]);


      // 提取各项指标数据
      const metrics = this.extractAppRankingMetrics(cells);


      // 构建达人数据对象 - 应用下载榜特有结构
      const influencerData = {
        rank: rank,
        timestamp: Date.now(),
        pageUrl: window.location.href,
        influencer: {
          name: influencerInfo.name,
          avatar: influencerInfo.avatar,
          followers: influencerInfo.followers  // 注意：应用下载榜使用 followers 而不是 fansCount
        },
        metrics: metrics,
        isApp: true,
        extractSource: 'oceanengine_app_ranking'
      };


      return influencerData;

    } catch (error) {
      console.error('提取应用下载榜行数据失败:', error);
      return null;
    }
  }

  // 提取应用下载榜指标数据
  extractAppRankingMetrics(cells) {
    const metrics = {
      componentClickRate: '',      // 组件点击率（第3列）
      videoPlayCount: '',          // 星图视频播放量（第4列）
      interactionRate: ''          // 互动率（第5列）
    };

    try {
      // 组件点击率（第3列，索引2）
      if (cells[2]) {
        metrics.componentClickRate = this.extractText(cells[2]).trim();

      }

      // 星图视频播放量（第4列，索引3）
      if (cells[3]) {
        metrics.videoPlayCount = this.extractText(cells[3]).trim();

      }

      // 互动率（第5列，索引4）
      if (cells[4]) {
        metrics.interactionRate = this.extractText(cells[4]).trim();

      }

      // 验证是否至少提取到一些指标
      const hasMetrics = Object.values(metrics).some(value => value.length > 0);
      if (!hasMetrics) {
        console.warn('❌ 未能提取到任何指标数据');
        for (let i = 0; i < cells.length; i++) {

        }
      }

    } catch (error) {
      console.error('提取应用下载榜指标失败:', error);
    }

    return metrics;
  }

  // 线索收集榜数据获取 - 待开发
  async extractClueRankingData(rankingType) {
    try {
      this.showNotification('正在获取线索收集榜数据...', 'default');

      // 查找表格主体
      const tableSelectors = [
        '.rank-list-table-body',
        '.rank-list-Table-Body',
        'table[role="table"] tbody',
        '.rank-list-table tbody',
        'tbody'
      ];

      let tableBody = null;
      for (const selector of tableSelectors) {
        const found = document.querySelector(selector);
        if (found) {
          tableBody = found;
          console.log('找到线索收集榜表格主体:', selector);
          break;
        }
      }

      if (!tableBody) {
        throw new Error('未找到线索收集榜表格');
      }

      // 查找数据行
      const rowSelectors = [
        '.rank-list-table-column.rank-list-table-row',
        '.rank-list-Table-Row',
        'tr[role="row"]',
        'tr:not(:first-child)',
        '.rank-list-table-column'
      ];

      let rows = [];
      for (const selector of rowSelectors) {
        const found = Array.from(tableBody.querySelectorAll(selector));
        if (found.length > 0) {
          rows = found;
          console.log(`找到${found.length}行线索收集榜数据`);
          break;
        }
      }

      // 如果没找到，尝试直接从tableBody获取
      if (rows.length === 0) {
        rows = Array.from(tableBody.children);
        console.log(`从tableBody直接获取到${rows.length}行数据`);
      }

      if (rows.length === 0) {
        console.warn('当前页面未找到线索收集榜数据行');
        return [];
      }

      // 提取每行数据
      const extractedData = [];
      let validDataCount = 0;

      for (const row of rows) {
        const rowData = this.extractClueRowData(row);
        if (rowData) {
          extractedData.push(rowData);
          validDataCount++;

          // 记录前三名数据
          if (rowData.rank <= 3) {
            console.log(`线索收集榜 Top ${rowData.rank}:`, JSON.stringify({
              rank: rowData.rank,
              name: rowData.influencer.name,
              clueCount: rowData.metrics.clueCount
            }));
          }
        }
      }

      console.log(`成功提取${validDataCount}条线索收集榜数据`);
      this.showNotification(`成功提取${validDataCount}条线索收集榜数据`, 'success');

      return extractedData;
    } catch (error) {
      console.error('线索收集榜数据获取失败:', error);
      this.sendErrorUpdate(`线索收集榜数据获取失败: ${error.message}`);
      return [];
    }
  }

  // 提取线索收集榜行数据
  extractClueRowData(row) {
    try {
      // 获取所有单元格
      const cellSelectors = [
        '.rank-list-table-cell',
        '.rank-list-Table-Cell',
        'td',
        'div[role="cell"]'
      ];

      let cells = [];
      for (const selector of cellSelectors) {
        const found = Array.from(row.querySelectorAll(selector));
        if (found.length > 0) {
          cells = found;
          break;
        }
      }

      if (cells.length < 5) {
        console.warn('线索收集榜行数据单元格数量不足:', cells.length);
        return null;
      }

      // 提取排名（第1列）
      const rankCell = cells[0];
      let rank = 0;

      // 检查是否有排名图片（前三名通常有特殊图标）
      const rankImg = rankCell.querySelector('img');
      if (rankImg && rankImg.src) {
        const src = rankImg.src;

        // 通过图片识别前三名
        if (src.includes('FEB332') || src.includes('gold') || src.includes('rank1')) {
          rank = 1;
        } else if (src.includes('C6CBD1') || src.includes('silver') || src.includes('rank2')) {
          rank = 2;
        } else if (src.includes('BF7C1F') || src.includes('bronze') || src.includes('rank3')) {
          rank = 3;
        }
      }

      // 如果没有通过图片识别到排名，尝试从文本提取
      if (rank === 0) {
        const rankText = this.extractText(rankCell);
        const rankMatch = rankText.match(/\d+/);
        if (rankMatch) {
          rank = parseInt(rankMatch[0]);
        }
      }

      // 如果仍然没有排名，可能是表格结构问题，尝试从行索引获取
      if (rank === 0) {
        try {
          const siblings = Array.from(row.parentNode.children);
          const rowIndex = siblings.indexOf(row);
          if (rowIndex >= 0) {
            rank = rowIndex + 1;
          }
        } catch (e) {
          console.warn('获取行索引失败:', e);
        }
      }

      // 提取达人信息（第2列）
      const influencerInfo = this.extractInfluencerInfo(cells[1]);

      // 提取线索收集量（第3列）
      const clueCountText = this.extractText(cells[2]);
      const clueCount = this.parseNumber(clueCountText);

      // 提取视频播放量（第4列）
      const playCountText = this.extractText(cells[3]);
      const playCount = this.parseNumber(playCountText);

      // 提取互动率（第5列）
      const interactionRateText = this.extractText(cells[4]);
      const interactionRate = interactionRateText.replace('%', '');

      // 构建数据对象
      const clueData = {
        rank: rank,
        timestamp: Date.now(),
        pageUrl: window.location.href,
        influencer: influencerInfo,
        metrics: {
          clueCount: clueCount,
          playCount: playCount,
          interactionRate: interactionRate
        },
        isClueRanking: true,
        extractSource: 'oceanengine_clue_ranking'
      };

      return clueData;
    } catch (error) {
      console.error('提取线索收集榜行数据失败:', error);
      return null;
    }
  }

  // 达人短视频主题榜数据获取 - 待开发
  async extractInfluencerVideoRankingData(rankingType) {
    try {

      this.showNotification('达人短视频主题榜获取功能开发中，敬请期待', 'warning');

      // TODO: 实现达人短视频主题榜的数据获取逻辑
      // 预期包含：达人信息、视频主题、影响力指标等

      return [];
    } catch (error) {
      this.sendErrorUpdate(`达人短视频主题榜数据获取失败: ${error.message}`);
      return [];
    }
  }

  // 营销热点榜数据获取
  async extractMarketingHotPotsRankingData(rankingType) {
    try {


      this.showNotification('开始获取营销热点榜数据...', 'info');

      // 清空营销热点榜数据，避免重复
      if (!this.extractedData.ranking) {
        this.extractedData.ranking = {};
      }
      this.extractedData.ranking.marketing_hot_pots = [];

      let allData = [];
      let currentPage = 1;
      let maxScrolls = 0; // 营销热点榜通常使用分页而非滚动

      // 持续提取数据直到没有更多数据或达到最大数量
      while (allData.length < this.maxExtractCount) {


        // 提取当前页面的数据
        const pageData = await this.extractCurrentPageMarketingHotPotsData(rankingType);


        if (pageData.length === 0) {

          break;
        }

        // 添加页面信息到每个数据项
        const pageDataWithInfo = pageData.map(item => ({
          ...item,
          currentPage: currentPage,
          pageUrl: window.location.href,
          timestamp: Date.now()
        }));

        allData.push(...pageDataWithInfo);

        // 发送进度更新
        this.sendProgressUpdate(
          Math.min(Math.round((allData.length / this.maxExtractCount) * 100), 100),
          `已获取 ${allData.length} 条营销热点榜数据 (第${currentPage}页)`
        );

        // 检查是否已达到最大获取数量
        if (allData.length >= this.maxExtractCount) {

          break;
        }

        // 尝试翻到下一页
        const hasNextPage = await this.goToNextPage();
        if (!hasNextPage) {

          break;
        }

        currentPage++;

        // 防止无限循环
        if (currentPage > 50) {

          break;
        }

        // 等待页面加载
        await this.waitForPageLoad();
      }

      // 限制返回的数据数量
      const finalData = allData.slice(0, this.maxExtractCount);

      // 更新获取计数
      this.extractCount = finalData.length;

      // 保存提取的数据
      if (finalData.length > 0) {
        this.saveExtractedData(rankingType, finalData);

      } else {
        console.warn('未能提取到任何营销热点榜数据');
      }


      this.showNotification(`营销热点榜获取完成，共 ${finalData.length} 条数据`, 'success');

      return finalData;

    } catch (error) {
      this.sendErrorUpdate(`营销热点榜数据获取失败: ${error.message}`);
      return [];
    }
  }

  // 提取当前页面的营销热点榜数据
  async extractCurrentPageMarketingHotPotsData(rankingType) {
    try {


      // 尝试多种选择器来查找营销热点榜容器
      const containerSelectors = [
        '[data-log-module="热点事件榜"]',
        '.HotMarketingRankList__Wrapper-hwfhiN',
        '.hot-event-list',
        '.hot-ranking-list',
        '[class*="HotMarketingRank"]',
        '[class*="hot-event"]',
        '[data-module="hot-event"]'
      ];

      let container = null;
      for (const selector of containerSelectors) {
        try {
          // 尝试等待元素加载
          await this.waitForElement(selector, 800);
          container = document.querySelector(selector);
          if (container) {

            break;
          }
        } catch (e) {

        }
      }

      // 如果还是没找到容器，尝试查找页面中所有可能的容器元素
      if (!container) {
        console.warn('未找到营销热点榜容器，尝试查找可能的容器元素');

        // 查找页面中的所有区域块
        const allSections = document.querySelectorAll('section, .section, [class*="Section"], [class*="List"], [class*="Container"]');
        for (const section of allSections) {
          // 检查文本内容是否包含相关关键词
          const text = section.textContent || '';
          if (text.includes('热点事件') || text.includes('营销热点') || text.includes('热点榜') ||
            text.includes('热度值') || text.includes('去蹭热点')) {
            container = section;

            break;
          }
        }
      }

      if (!container) {
        console.warn('未找到营销热点榜容器，无法提取数据');
        return [];
      }




      // 查找所有营销热点项 - 尝试多种选择器
      const itemSelectors = [
        '.HotMarketingRankList__Item-iEsmWe',
        '.hot-event-item',
        '.ranking-item',
        '[class*="HotMarketingRankList__Item"]',
        '[class*="hot-event-item"]',
        '[class*="ranking-item"]',
        'a[href*="hot_event"]',
        '.list-item'
      ];

      let hotItems = [];
      for (const selector of itemSelectors) {
        const items = container.querySelectorAll(selector);
        if (items && items.length > 0) {

          hotItems = Array.from(items);
          break;
        }
      }

      // 如果使用选择器没有找到，尝试智能识别列表项
      if (hotItems.length === 0) {


        // 查找直接子元素中可能的列表项
        const childDivs = Array.from(container.children).filter(el =>
          el.tagName === 'DIV' || el.tagName === 'LI' || el.tagName === 'A'
        );

        if (childDivs.length > 0) {
          // 如果直接子元素较少，它们可能是包装容器而不是实际的列表项
          if (childDivs.length === 1 || childDivs.length === 2) {
            // 查找下一级子元素
            const nestedItems = childDivs[0].querySelectorAll('div, li, a');
            if (nestedItems.length > 2) {
              hotItems = Array.from(nestedItems);

            }
          } else if (childDivs.length >= 3) {
            // 如果有多个直接子元素，它们可能是实际的列表项
            hotItems = childDivs;

          }
        }

        // 如果还是没找到，最后尝试过滤所有div元素
        if (hotItems.length === 0) {
          const allDivs = Array.from(container.querySelectorAll('div'));


          // 找出具有相似结构和内容的div，它们可能是列表项
          const potentialItems = allDivs.filter(div => {
            // 查找包含排名图标和热点名称的容器
            return (div.querySelector('img') || div.querySelector('span')) &&
              (div.textContent.includes('热度值') ||
                div.textContent.match(/\d+/) ||
                div.querySelector('[data-log-label="去蹭热点"]'));
          });

          // 从潜在项中选择最可能的项
          if (potentialItems.length >= 3) {
            // 找到所有潜在项的共同父元素
            const parents = {};
            potentialItems.forEach(item => {
              const parent = item.parentElement;
              if (parent) {
                parents[parent.className] = parents[parent.className] || [];
                parents[parent.className].push(parent);
              }
            });

            // 查找最常见的父元素类名
            let maxCount = 0;
            let commonParentClass = '';
            for (const [className, elements] of Object.entries(parents)) {
              if (elements.length > maxCount) {
                maxCount = elements.length;
                commonParentClass = className;
              }
            }

            // 如果找到共同父元素，使用其子元素作为热点项
            if (commonParentClass && maxCount >= 3) {
              const commonParent = document.querySelector(`.${commonParentClass}`);
              if (commonParent) {
                hotItems = Array.from(commonParent.children);

              }
            } else {
              // 否则直接使用潜在项
              hotItems = potentialItems;

            }
          }
        }
      }



      if (hotItems.length === 0) {
        console.warn('未找到营销热点项，获取失败');
        return [];
      }

      const extractedData = [];

      // 提取每个热点项的数据
      for (let i = 0; i < hotItems.length; i++) {
        try {
          const item = hotItems[i];
          const hotData = this.extractMarketingHotPotItemData(item, i + 1);

          if (hotData) {
            extractedData.push(hotData);
          }
        } catch (error) {
          console.error(`提取第 ${i + 1} 个营销热点项数据失败:`, error);
        }
      }


      return extractedData;

    } catch (error) {
      console.error('提取当前页面营销热点榜数据失败:', error);
      return [];
    }
  }

  // 提取单个营销热点项数据
  extractMarketingHotPotItemData(item, defaultRank) {
    try {



      const data = {
        rank: defaultRank,
        name: '',
        description: '',
        level: '',
        hotValue: '',
        participatingStores: '',
        relatedProducts: '',
        relatedVideos: '',
        rankingType: 'marketing_hot_pots',
        isMarketingHotPot: true // 标识这是营销热点榜数据
      };

      // 提取排名 - 首先查找特定结构的排名元素
      const rankElements = [
        item.querySelector('[class*="rank"], [class*="Rank"]'),
        item.querySelector('img[src*="data:image/svg"]'),
        item.querySelector('.rank'),
        item.querySelector('.ranking'),
        item.querySelector('div:first-child')
      ].filter(Boolean);

      // 如果找到排名元素，尝试从中提取排名
      if (rankElements.length > 0) {
        for (const rankElement of rankElements) {
          // 尝试从文本内容中提取数字
          const rankText = rankElement.textContent || '';
          const rankMatch = rankText.match(/(\d+)/);

          if (rankMatch && parseInt(rankMatch[1]) <= 100) {
            data.rank = parseInt(rankMatch[1]);

            break;
          }

          // 尝试从class名或ID中提取排名
          const classList = Array.from(rankElement.classList || []);
          const rankClassMatch = classList.find(cls => /rank[-_]?(\d+)/.test(cls));

          if (rankClassMatch) {
            const match = rankClassMatch.match(/rank[-_]?(\d+)/);
            if (match && parseInt(match[1]) <= 100) {
              data.rank = parseInt(match[1]);

              break;
            }
          }

          // 检查兄弟元素中是否有排名
          const siblings = rankElement.parentElement ?
            Array.from(rankElement.parentElement.children) : [];

          for (const sibling of siblings) {
            const siblingText = sibling.textContent || '';
            const siblingMatch = siblingText.match(/^(\d+)$/);

            if (siblingMatch && parseInt(siblingMatch[1]) <= 100) {
              data.rank = parseInt(siblingMatch[1]);

              break;
            }
          }
        }
      }

      // 如果上面的方法都没提取到排名，尝试从整个项中查找
      if (data.rank === defaultRank) {
        // 提取所有文本内容
        const allText = item.textContent || '';

        // 查找开头的数字作为排名
        const startDigitMatch = allText.match(/^\s*(\d+)/);
        if (startDigitMatch && parseInt(startDigitMatch[1]) <= 100) {
          data.rank = parseInt(startDigitMatch[1]);

        }

        // 查找#数字格式的排名
        const hashDigitMatch = allText.match(/#(\d+)/);
        if (hashDigitMatch && parseInt(hashDigitMatch[1]) <= 100) {
          data.rank = parseInt(hashDigitMatch[1]);

        }
      }

      // 提取等级标签 - 查找包含"级"的文本
      const levelElements = Array.from(item.querySelectorAll('*')).filter(el => {
        const text = el.textContent?.trim() || '';
        return text && (text === 'S级' || text === 'A级' || text === 'B级' || text === 'C级' ||
          text === 'S' || text === 'A' || text === 'B' || text === 'C');
      });

      if (levelElements.length > 0) {
        data.level = levelElements[0].textContent.trim();

      } else {
        // 尝试从全文中匹配等级
        const allText = item.textContent || '';
        const levelMatch = allText.match(/[SABC][级]?/);
        if (levelMatch) {
          data.level = levelMatch[0];

        }
      }

      // 提取热点名称 - 查找最可能的标题元素
      const titleSelectors = [
        '[class*="title"]',
        '[class*="Title"]',
        '[class*="name"]',
        '[class*="Name"]',
        'h1, h2, h3, h4, h5',
        'a[href*="hot_event"]',
        'a[href*="hot-event"]'
      ];

      for (const selector of titleSelectors) {
        const titleElement = item.querySelector(selector);
        if (titleElement) {
          const titleText = titleElement.textContent?.trim();
          if (titleText && titleText.length > 2 &&
            !titleText.includes('热度值') &&
            !titleText.match(/^\d+$/) &&
            !titleText.includes('级')) {
            data.name = titleText;

            break;
          }
        }
      }

      // 如果没找到标题，尝试找到最有可能的文本内容
      if (!data.name) {
        // 查找所有文本元素
        const textElements = Array.from(item.querySelectorAll('*')).filter(el => {
          const text = el.textContent?.trim();
          return text &&
            text.length > 3 &&
            text.length < 100 &&
            !text.includes('热度值') &&
            !text.includes('万') &&
            !text.includes('店铺') &&
            !text.includes('商品') &&
            !text.includes('视频') &&
            !text.includes('去蹭热点') &&
            !text.match(/^\d+$/) &&
            !text.includes('级');
        });

        // 根据一些特征找到最可能的标题元素 
        if (textElements.length > 0) {
          // 按文本长度排序 - 标题通常是中等长度的文本
          textElements.sort((a, b) => {
            const aText = a.textContent?.trim() || '';
            const bText = b.textContent?.trim() || '';
            // 优先选择长度适中的文本作为标题
            const aScore = Math.abs(aText.length - 15); // 15字符是理想长度
            const bScore = Math.abs(bText.length - 15);
            return aScore - bScore;
          });

          data.name = textElements[0].textContent?.trim() || '';

        }
      }

      // 提取热点描述 - 通常在名称之后的较长文本
      if (data.name) {
        const descElements = Array.from(item.querySelectorAll('*')).filter(el => {
          const text = el.textContent?.trim();
          return text &&
            text !== data.name &&
            text.length > data.name.length &&
            !text.includes('热度值') &&
            !text.includes('万') &&
            !text.includes('店铺') &&
            !text.match(/^\d+$/);
        });

        if (descElements.length > 0) {
          // 按照文本长度降序排列 - 描述通常较长
          descElements.sort((a, b) => {
            const aText = a.textContent?.trim() || '';
            const bText = b.textContent?.trim() || '';
            return bText.length - aText.length;
          });

          data.description = descElements[0].textContent?.trim() || '';

        }
      }

      // 提取各种指标
      const allText = item.textContent || '';

      // 提取热度值
      const hotValuePatterns = [
        /热度值[：:]\s*([0-9,.]+\s*万?)/,
        /热度[：:]\s*([0-9,.]+\s*万?)/,
        /热度[值]?\s*([0-9,.]+\s*万?)/
      ];

      for (const pattern of hotValuePatterns) {
        const match = allText.match(pattern);
        if (match) {
          data.hotValue = match[1].trim();

          break;
        }
      }

      // 提取参与店铺数
      const storesPatterns = [
        /参与店铺[：:]\s*([0-9,.]+[万]?)/,
        /店铺[：:]\s*([0-9,.]+[万]?)/,
        /参与[：:]\s*([0-9,.]+[万]?家)/
      ];

      for (const pattern of storesPatterns) {
        const match = allText.match(pattern);
        if (match) {
          data.participatingStores = match[1].trim();

          break;
        }
      }

      // 提取相关商品数
      const productsPatterns = [
        /相关商品[：:]\s*([0-9,.]+[万]?)/,
        /商品[：:]\s*([0-9,.]+[万]?)/,
        /商品数[：:]\s*([0-9,.]+[万]?)/
      ];

      for (const pattern of productsPatterns) {
        const match = allText.match(pattern);
        if (match) {
          data.relatedProducts = match[1].trim();

          break;
        }
      }

      // 提取相关视频数
      const videosPatterns = [
        /相关视频[：:]\s*([0-9,.]+)/,
        /视频[：:]\s*([0-9,.]+)/,
        /视频数[：:]\s*([0-9,.]+)/
      ];

      for (const pattern of videosPatterns) {
        const match = allText.match(pattern);
        if (match) {
          data.relatedVideos = match[1].trim();

          break;
        }
      }

      // 数据验证和完善

      // 如果没提取到名称，使用描述作为备选
      if (!data.name && data.description) {
        data.name = data.description;
        data.description = '';

      }

      // 如果没有提取到名称，再尝试从链接中提取
      if (!data.name) {
        const links = item.querySelectorAll('a');
        for (const link of links) {
          const href = link.getAttribute('href') || '';
          const linkText = link.textContent?.trim();

          if (href.includes('hot_event') && linkText && linkText.length > 2) {
            data.name = linkText;

            break;
          }
        }
      }

      // 数据验证 - 至少要有热点名称或排名
      if (!data.name && data.rank === defaultRank) {

        return null;
      }


      return data;

    } catch (error) {
      console.error('提取营销热点项数据失败:', error);
      return null;
    }
  }

  // 创作热点榜数据获取 - 待开发
  async extractHotTopicRankingData(rankingType) {
    try {

      this.showNotification('创作热点榜获取功能开发中，敬请期待', 'warning');

      // TODO: 实现创作热点榜的数据获取逻辑
      // 预期包含：创作热点内容、关注度、传播指标等

      return [];
    } catch (error) {
      this.sendErrorUpdate(`创作热点榜数据获取失败: ${error.message}`);
      return [];
    }
  }

  // 创作热词榜数据获取 - 待开发
  async extractHotWordsRankingData(rankingType) {
    try {

      this.showNotification('创作热词榜获取功能开发中，敬请期待', 'warning');

      // TODO: 实现创作热词榜的数据获取逻辑
      // 预期包含：创作热词内容、关注度、传播指标等

      return [];
    } catch (error) {
      this.sendErrorUpdate(`创作热词榜数据获取失败: ${error.message}`);
      return [];
    }
  }

  // 提取应用下载榜达人信息 - 专门方法
  extractAppInfluencerInfo(cell) {
    const influencerInfo = {
      name: '',
      avatar: '',
      followers: ''
    };

    try {


      // 查找应用下载榜达人信息容器
      const infoContainers = [
        '.useTableColumnsComponents__StarBox-dJlgxR',
        '.influencer-info',
        '.avatar-info',
        '.user-info'
      ];

      let infoContainer = null;
      for (const selector of infoContainers) {
        const found = cell.querySelector(selector);
        if (found) {
          infoContainer = found;

          break;
        }
      }

      // 如果没找到容器，直接在cell中查找
      if (!infoContainer) {
        infoContainer = cell;

      }

      // 提取头像
      const avatarSelectors = [
        '.avatar',
        'img[src*="douyinpic.com"]',
        'img[src*="avatar"]',
        '.user-avatar img',
        'img'
      ];

      for (const selector of avatarSelectors) {
        const avatarElement = infoContainer.querySelector(selector);
        if (avatarElement && avatarElement.src) {
          influencerInfo.avatar = avatarElement.src;

          break;
        }
      }

      // 提取达人昵称 - 应用下载榜特有结构
      const nameSelectors = [
        '.name',
        '.info .name',
        '.info .name > div',
        '.user-name',
        '.influencer-name',
        '[class*="name"]',
        '.useTableColumnsComponents__Ellipsis-cNHHLp'
      ];

      for (const selector of nameSelectors) {
        const nameElement = infoContainer.querySelector(selector);
        if (nameElement) {
          const nameText = this.extractText(nameElement).trim();
          if (nameText && nameText.length > 0) {
            influencerInfo.name = nameText;

            break;
          }
        }
      }

      // 如果还没找到名称，尝试从更通用的元素中提取
      if (!influencerInfo.name) {
        const textElements = infoContainer.querySelectorAll('div, span');
        for (const element of textElements) {
          const text = this.extractText(element).trim();
          // 排除粉丝数等信息，查找可能的用户名
          if (text && text.length > 0 && text.length < 50 &&
            !text.includes('粉丝') && !text.includes('万') &&
            !text.includes('%') && !text.includes('w')) {
            influencerInfo.name = text;

            break;
          }
        }
      }

      // 提取粉丝数 - 应用下载榜特有结构
      const followersSelectors = [
        '.fans .num',
        '.fans-count',
        '.follower-count',
        '.info .fans .num',
        '[class*="fans"] .num',
        '[class*="follower"] .num'
      ];

      for (const selector of followersSelectors) {
        const followersElement = infoContainer.querySelector(selector);
        if (followersElement) {
          const followersText = this.extractText(followersElement).trim();
          if (followersText && followersText.length > 0) {
            influencerInfo.followers = followersText;

            break;
          }
        }
      }

      // 如果还没找到粉丝数，尝试从包含"粉丝"的文本中提取
      if (!influencerInfo.followers) {
        const textElements = infoContainer.querySelectorAll('div, span');
        for (const element of textElements) {
          const text = this.extractText(element).trim();
          if (text.includes('粉丝数：') || text.includes('粉丝：')) {
            const fansMatch = text.match(/粉丝数?：?(\d+[万wWkK]?)/);
            if (fansMatch) {
              influencerInfo.followers = fansMatch[1];

              break;
            }
          }
        }
      }

      // 验证提取结果
      if (!influencerInfo.name && !influencerInfo.avatar) {
        console.warn('❌ 未能提取到应用下载榜达人基本信息');


        // 输出所有可能的文本内容以便调试
        const allText = Array.from(cell.querySelectorAll('*'))
          .map(el => this.extractText(el).trim())
          .filter(text => text.length > 0);

      }

    } catch (error) {
      console.error('提取应用下载榜达人信息失败:', error);
    }

    return influencerInfo;
  }
}

// 初始化获取器
const oceanEngineExtractor = new OceanEngineRankingExtractor();

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {

});