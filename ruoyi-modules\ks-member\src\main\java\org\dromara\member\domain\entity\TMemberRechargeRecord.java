package org.dromara.member.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 会员充值记录对象 t_member_recharge_record
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_member_recharge_record")
public class TMemberRechargeRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 会员ID
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 充值商品ID
     */
    @TableField("goods_id")
    private Long goodsId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 充值金额（分）
     */
    @TableField("payment_amount")
    private Long rechargeAmount;

    /**
     * 获得积分数
     */
    @TableField("points_amount")
    private Long pointsAmount;

    /**
     * 支付方式
     */
    @TableField("payment_method")
    private String paymentMethod;

    /**
     * 支付时间
     */
    @TableField("payment_time")
    private Date rechargeTime;

    /**
     * 支付状态：success=成功，failed=失败，pending=处理中
     */
    @TableField("payment_status")
    private String status;

    /**
     * 交易流水号
     */
    @TableField("transaction_id")
    private String transactionId;
}
