package org.dromara.member.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会员积分批次视图对象 t_member_points_batch
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@ExcelIgnoreUnannotated
public class TMemberPointsBatchVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 批次类型
     */
    @ExcelProperty(value = "批次类型")
    private String batchType;

    /**
     * 来源ID
     */
    @ExcelProperty(value = "来源ID")
    private String sourceId;

    /**
     * 总积分数
     */
    @ExcelProperty(value = "总积分数")
    private Long totalPoints;

    /**
     * 剩余积分数
     */
    @ExcelProperty(value = "剩余积分数")
    private Long remainingPoints;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    private Date expireDate;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "points_batch_status")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;
}
