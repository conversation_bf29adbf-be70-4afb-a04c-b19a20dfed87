package org.dromara.product.apiconfig.service;

/**
 * API Token验证服务接口
 * 用于在product模块中验证API Token并获取member_id
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface IApiTokenValidationService {

    /**
     * 根据API Token获取会员ID
     *
     * @param apiToken API Token
     * @return 会员ID，如果Token无效则返回null
     */
    Long getMemberIdByApiToken(String apiToken);

    /**
     * 验证API Token是否有效
     *
     * @param apiToken API Token
     * @return 是否有效
     */
    boolean isValidApiToken(String apiToken);
}
