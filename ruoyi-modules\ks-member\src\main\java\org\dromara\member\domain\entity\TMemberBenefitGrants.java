package org.dromara.member.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 会员权益生效记录对象 t_member_benefit_grants
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_member_benefit_grants")
public class TMemberBenefitGrants extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 会员ID
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 权益配置ID
     */
    @TableField("benefit_config_id")
    private Long benefitConfigId;

    /**
     * 旧等级
     */
    @TableField("old_level")
    private Integer oldLevel;

    /**
     * 新等级
     */
    @TableField("new_level")
    private Integer newLevel;

    /**
     * 授予时间
     */
    @TableField("grant_date")
    private Date grantDate;

    /**
     * 生效时间
     */
    @TableField("effective_date")
    private Date effectiveDate;

    /**
     * 过期时间
     */
    @TableField("expiry_date")
    private Date expiryDate;

    /**
     * 状态：active=生效中，expired=已过期，revoked=已撤销
     */
    @TableField("status")
    private String status;
}
