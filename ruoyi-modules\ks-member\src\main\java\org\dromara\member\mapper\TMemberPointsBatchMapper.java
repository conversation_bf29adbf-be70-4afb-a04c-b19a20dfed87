package org.dromara.member.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.member.domain.entity.TMemberPointsBatch;
import org.dromara.member.domain.vo.TMemberPointsBatchVo;

import java.util.List;

/**
 * 会员积分批次Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface TMemberPointsBatchMapper extends BaseMapperPlus<TMemberPointsBatch, TMemberPointsBatchVo> {

    /**
     * 查询会员有效的积分批次（按过期时间升序）
     */
    @Select("SELECT * FROM t_member_points_batch " +
            "WHERE member_id = #{memberId} AND status = 'active' " +
            "AND remaining_points > 0 AND expire_date > NOW() AND del_flag = '0' " +
            "ORDER BY expire_date ASC")
    List<TMemberPointsBatch> selectValidBatchesByMemberId(@Param("memberId") Long memberId);

    /**
     * 更新批次剩余积分
     */
    @Update("UPDATE t_member_points_batch SET remaining_points = #{remainingPoints}, " +
            "status = CASE WHEN #{remainingPoints} <= 0 THEN 'consumed' ELSE status END, " +
            "update_time = NOW() WHERE id = #{batchId}")
    int updateRemainingPoints(@Param("batchId") Long batchId, @Param("remainingPoints") Long remainingPoints);

    /**
     * 标记过期批次
     */
    @Update("UPDATE t_member_points_batch SET status = 'expired', update_time = NOW() " +
            "WHERE status = 'active' AND expire_date <= NOW() AND del_flag = '0'")
    int markExpiredBatches();
}


