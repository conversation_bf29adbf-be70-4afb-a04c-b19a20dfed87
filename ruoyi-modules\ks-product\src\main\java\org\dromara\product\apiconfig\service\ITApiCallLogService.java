package org.dromara.product.apiconfig.service;

import org.dromara.product.apiconfig.domain.bo.TApiCallLogBo;
import org.dromara.product.apiconfig.domain.vo.TApiCallLogVo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * API调用日志Service接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface ITApiCallLogService {

    /**
     * 查询API调用日志
     */
    TApiCallLogVo queryById(Long id);

    /**
     * 查询API调用日志列表
     */
    TableDataInfo<TApiCallLogVo> queryPageList(TApiCallLogBo bo, PageQuery pageQuery);

    /**
     * 查询API调用日志列表
     */
    List<TApiCallLogVo> queryList(TApiCallLogBo bo);

    /**
     * 根据新增业务对象插入API调用日志
     */
    Boolean insertByBo(TApiCallLogBo bo);

    /**
     * 根据编辑业务对象修改API调用日志
     */
    Boolean updateByBo(TApiCallLogBo bo);

    /**
     * 校验并批量删除API调用日志信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取API调用统计信息
     */
    Object getApiCallStats(TApiCallLogBo bo);

    /**
     * 清理过期日志
     */
    Boolean cleanExpiredLogs(Integer days);
}
