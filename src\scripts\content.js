// 抖音用户页面数据获取器 - Content Script

// 全局错误捕获
window.addEventListener('error', (event) => {
  if (event.error && event.error.message && event.error.message.includes('Extension context invalidated')) {
    event.preventDefault();
    handleExtensionContextError('检测到扩展上下文失效');
  }
});

// 扩展上下文检查和自动恢复
function checkExtensionContext() {
  try {
    return chrome.runtime && chrome.runtime.id;
  } catch (error) {
    return false;
  }
}

function handleExtensionContextError(message = '扩展上下文失效') {
  console.error(message);

  // 停止所有正在进行的操作
  if (window.extractor && window.extractor.isExtracting) {
    window.extractor.isExtracting = false;
    if (window.extractor.autoScrollInterval) {
      clearInterval(window.extractor.autoScrollInterval);
    }
  }

  if (window.videoDetailExtractor && window.videoDetailExtractor.isExtracting) {
    window.videoDetailExtractor.isExtracting = false;
  }

  // 显示通知
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    background: #ff4757;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  `;
  notification.textContent = `${message}，3秒后自动刷新页面...`;
  document.body.appendChild(notification);

  // 3秒后自动刷新
  setTimeout(() => {
    window.location.reload();
  }, 3000);
}

// 页面状态管理器 - 统一管理页面类型检测和浮动按钮
class PageStateManager {
  constructor() {
    this.currentPageType = 'unknown';
    this.observer = null;
    this.userFloatingButton = null;
    this.videoFloatingButton = null;
    this.debounceTimer = null;
    this.isInitialized = false;
  }

  // 初始化状态管理器
  init() {
    if (this.isInitialized) return;

    this.detectInitialPageType();
    this.setupDOMObserver();
    this.setupHashChangeListener();
    this.isInitialized = true;
  }

  // 检测初始页面类型
  detectInitialPageType() {
    const newPageType = this.detectPageType();
    if (newPageType !== this.currentPageType) {
      this.currentPageType = newPageType;
      this.updateFloatingButtons();
    }
  }

  // 检测当前页面类型
  detectPageType() {
    const url = window.location.href;

    // 检查是否有视频播放器
    const videoPlayerSelectors = [
      '[data-e2e="video-player"]',
      '.xgplayer',
      '.video-player-container',
      '.player-container',
      'video'
    ];

    let hasVideoPlayer = false;
    for (const selector of videoPlayerSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        hasVideoPlayer = true;
        break;
      }
    }

    // 如果有视频播放器，判断为视频详情页
    if (hasVideoPlayer) {
      return 'videoDetail';
    }

    // 检查是否为用户页面
    const isUserUrl = url.includes('/user/');

    // 备用检测：检查多种可能的用户页面特征
    const backupIsUser =
      url.match(/\/[a-zA-Z0-9_-]+\?/) || // 匹配类似 /username? 的格式
      url.match(/douyin\.com\/[a-zA-Z0-9_-]+$/) || // 匹配 douyin.com/username 格式
      document.querySelector('[data-e2e="user-info"]') !== null; // 通过DOM元素判断

    if (isUserUrl || backupIsUser) {
      return 'user';
    }

    return 'unknown';
  }

  // 设置DOM变化观察器
  setupDOMObserver() {
    if (this.observer) {
      this.observer.disconnect();
    }

    this.observer = new MutationObserver((mutations) => {
      // 使用防抖机制，避免频繁触发
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.debounceTimer = setTimeout(() => {
        this.handleDOMChange(mutations);
      }, 200); // 减少防抖时间，提高响应速度
    });

    // 开始观察DOM变化 - 扩展监听范围
    this.observer.observe(document.body, {
      childList: true,        // 监听子元素添加/移除
      subtree: true,          // 监听所有后代元素
      attributes: true,       // 监听属性变化
      attributeFilter: [      // 只监听特定属性
        'class',
        'style',
        'data-e2e',
        'data-testid',
        'data-player-state',
        'aria-hidden',
        'hidden'
      ]
    });

  }

  // 设置URL hash变化监听器
  setupHashChangeListener() {
    // 使用箭头函数保持this上下文
    this.handleHashChange = () => {
      // 延迟检测，等待页面更新
      setTimeout(() => {
        this.detectInitialPageType();
      }, 300);
    };

    this.handlePopState = () => {
      setTimeout(() => {
        this.detectInitialPageType();
      }, 300);
    };

    window.addEventListener('hashchange', this.handleHashChange);
    window.addEventListener('popstate', this.handlePopState);
  }

  // 处理DOM变化
  handleDOMChange(mutations) {
    // 检查是否有关键元素的变化
    let hasSignificantChange = false;

    for (const mutation of mutations) {
      if (mutation.type === 'childList') {
        // 检查是否有视频播放器相关元素的添加或移除
        const addedNodes = Array.from(mutation.addedNodes);
        const removedNodes = Array.from(mutation.removedNodes);

        const allNodes = [...addedNodes, ...removedNodes];

        for (const node of allNodes) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 检查是否包含视频播放器相关元素
            const hasVideoElements = node.querySelector && (
              node.querySelector('[data-e2e="video-player"]') ||
              node.querySelector('.xgplayer') ||
              node.querySelector('.video-player-container') ||
              node.querySelector('.player-container') ||
              node.matches && (
                node.matches('[data-e2e="video-player"]') ||
                node.matches('.xgplayer') ||
                node.matches('.video-player-container') ||
                node.matches('.player-container')
              )
            );

            if (hasVideoElements) {
              hasSignificantChange = true;
              break;
            }
          }
        }
      } else if (mutation.type === 'attributes') {
        // 检查属性变化是否影响视频播放器
        const target = mutation.target;
        const attributeName = mutation.attributeName;

        // 检查是否是视频播放器相关元素的属性变化
        if (target.nodeType === Node.ELEMENT_NODE) {
          const isVideoElement =
            target.matches('[data-e2e="video-player"]') ||
            target.matches('.xgplayer') ||
            target.matches('.video-player-container') ||
            target.matches('.player-container') ||
            target.querySelector('[data-e2e="video-player"]') ||
            target.querySelector('.xgplayer') ||
            target.querySelector('.video-player-container');

          // 或者检查是否是可能影响模态框显示的属性变化
          const isModalRelated =
            attributeName === 'class' && (
              target.className.includes('modal') ||
              target.className.includes('dialog') ||
              target.className.includes('popup') ||
              target.className.includes('overlay')
            ) ||
            attributeName === 'style' ||
            attributeName === 'aria-hidden' ||
            attributeName === 'hidden';

          if (isVideoElement || isModalRelated) {
            hasSignificantChange = true;
          }
        }
      }

      if (hasSignificantChange) break;
    }

    if (hasSignificantChange) {
      this.detectInitialPageType();
    }
  }

  // 更新浮动按钮
  updateFloatingButtons() {
    // 移除现有按钮
    this.removeAllFloatingButtons();

    // 根据页面类型创建对应按钮
    if (this.currentPageType === 'user') {
      this.createUserFloatingButton();
    } else if (this.currentPageType === 'videoDetail') {
      this.createVideoFloatingButton();
    }

    // 通知popup状态变化
    this.notifyPopupStateChange();
  }

  // 移除所有浮动按钮
  removeAllFloatingButtons() {
    // 移除用户页面按钮
    const existingUserBtn = document.getElementById('douyin-extractor-btn');
    if (existingUserBtn) {
      existingUserBtn.remove();
    }

    // 移除视频详情页按钮
    const existingVideoBtn = document.getElementById('douyin-detail-extractor-btn');
    if (existingVideoBtn) {
      existingVideoBtn.remove();
    }

    this.userFloatingButton = null;
    this.videoFloatingButton = null;
  }

  // 创建用户页面浮动按钮
  createUserFloatingButton() {
    const floatingButton = document.createElement('div');
    floatingButton.id = 'douyin-extractor-btn';
    floatingButton.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      background: #28a745;
      color: white;
      padding: 10px 15px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 12px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      user-select: none;
      min-width: 120px;
      text-align: center;
      transition: all 0.3s ease;
    `;
    floatingButton.textContent = '获取用户作品';

    // 绑定点击事件
    floatingButton.addEventListener('click', () => {
      // 检查扩展上下文
      if (!checkExtensionContext()) {
        handleExtensionContextError('扩展上下文失效');
        return;
      }

      if (extractor.isExtracting) {
        extractor.stopExtraction();
      } else {
        // 开始抓取前，提示用户打开扩展面板查看进度
        extractor.showPopupHint();
        extractor.startExtraction(100);
      }
    });

    // 添加鼠标悬停效果
    floatingButton.addEventListener('mouseenter', () => {
      floatingButton.style.transform = 'scale(1.05)';
    });

    floatingButton.addEventListener('mouseleave', () => {
      floatingButton.style.transform = 'scale(1)';
    });

    document.body.appendChild(floatingButton);
    this.userFloatingButton = floatingButton;

    // 将按钮引用传递给extractor
    extractor.floatingButton = floatingButton;
  }

  // 创建视频详情页浮动按钮
  createVideoFloatingButton() {
    const floatingButton = document.createElement('div');
    floatingButton.id = 'douyin-detail-extractor-btn';
    floatingButton.style.cssText = `
      position: fixed;
      top: 70px;
      right: 20px;
      z-index: 9999;
      background: #fe2c55;
      color: white;
      padding: 10px 15px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 12px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      user-select: none;
      min-width: 120px;
      text-align: center;
      transition: all 0.3s ease;
    `;
    floatingButton.textContent = '获取视频详情';

    // 绑定点击事件
    floatingButton.addEventListener('click', () => {
      // 检查扩展上下文
      if (!checkExtensionContext()) {
        handleExtensionContextError('扩展上下文失效');
        return;
      }

      // 确保videoDetailExtractor存在
      if (!window.videoDetailExtractor) {
        // 如果不存在，尝试创建一个
        if (typeof DouyinVideoDetailExtractor !== 'undefined') {
          window.videoDetailExtractor = new DouyinVideoDetailExtractor();
        } else {
          console.error('DouyinVideoDetailExtractor 未定义');
          return;
        }
      }

      if (window.videoDetailExtractor.isExtracting) {
        window.videoDetailExtractor.stopExtraction();
        floatingButton.textContent = '获取视频详情';
        floatingButton.style.background = '#fe2c55';
      } else {
        // 显示popup提示
        extractor.showPopupHint();
        window.videoDetailExtractor.startExtraction(100);
        floatingButton.textContent = '停止获取';
        floatingButton.style.background = '#dc3545';
      }
    });

    // 添加鼠标悬停效果
    floatingButton.addEventListener('mouseenter', () => {
      floatingButton.style.transform = 'scale(1.05)';
    });

    floatingButton.addEventListener('mouseleave', () => {
      floatingButton.style.transform = 'scale(1)';
    });

    document.body.appendChild(floatingButton);
    this.videoFloatingButton = floatingButton;
  }

  // 通知popup状态变化
  notifyPopupStateChange() {
    if (!checkExtensionContext()) {
      return; // 静默失败，不触发自动刷新
    }

    try {
      chrome.runtime.sendMessage({
        type: 'PAGE_STATE_CHANGED',
        pageType: this.currentPageType,
        timestamp: Date.now()
      }).catch(error => {
        // 忽略popup未打开时的错误
      });
    } catch (error) {
      // 忽略通信错误
    }
  }

  // 获取当前页面状态
  getCurrentState() {
    return {
      pageType: this.currentPageType,
      hasUserButton: !!this.userFloatingButton,
      hasVideoButton: !!this.videoFloatingButton,
      isUserExtracting: extractor ? extractor.isExtracting : false,
      isVideoExtracting: window.videoDetailExtractor ? window.videoDetailExtractor.isExtracting : false
    };
  }

  // 强制重新检测页面类型（用于调试）
  forceRedetect() {
    this.detectInitialPageType();
  }

  // 强制更新浮动按钮（用于调试）
  forceUpdateButtons() {
    this.updateFloatingButtons();
  }



    // 检查页面上的按钮
    const userBtn = document.getElementById('douyin-extractor-btn');
    const videoBtn = document.getElementById('douyin-detail-extractor-btn');
    console.log('页面上的按钮:');
    console.log('  用户按钮:', userBtn ? userBtn.textContent : '不存在');
    console.log('  视频按钮:', videoBtn ? videoBtn.textContent : '不存在');

    return this.getCurrentState();
  }

  // 销毁状态管理器
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    // 清理事件监听器
    window.removeEventListener('hashchange', this.handleHashChange);
    window.removeEventListener('popstate', this.handlePopState);

    this.removeAllFloatingButtons();
    this.isInitialized = false;
  }
}

class DouyinUserPageExtractor {
  constructor() {
    this.extractedData = [];
    this.isExtracting = false;
    this.maxExtractCount = 100;
    this.currentExtractCount = 0;
    this.extractedVideoIds = new Set();
    this.autoScrollInterval = null;
    this.floatingButton = null;
    this.userInfo = null;
  }

  // 检测是否为用户页面
  isUserPage() {
    const url = window.location.href;

    // 首先检查URL是否包含/user/
    const isUserUrl = url.includes('/user/');

    // 然后检查是否没有视频播放组件
    const videoPlayerSelectors = [
      '[data-e2e="video-player"]',
      '.xgplayer',
      '.video-player-container',
      '.player-container',
      'video'
    ];

    let hasVideoPlayer = false;
    for (const selector of videoPlayerSelectors) {
      if (document.querySelector(selector)) {
        hasVideoPlayer = true;
        break;
      }
    }

    // 用户页面需要满足：URL包含/user/ 且 没有视频播放组件
    const isUser = isUserUrl && !hasVideoPlayer;

    // 如果URL不包含/user/，使用备用检测逻辑
    if (!isUserUrl) {
      // 备用检测：检查多种可能的用户页面特征
      const backupIsUser =
        url.match(/\/[a-zA-Z0-9_-]+\?/) || // 匹配类似 /username? 的格式
        url.match(/douyin\.com\/[a-zA-Z0-9_-]+$/) || // 匹配 douyin.com/username 格式
        document.querySelector('[data-e2e="user-info"]') !== null; // 通过DOM元素判断

      return backupIsUser && !hasVideoPlayer;
    }

    return isUser;
  }

  // 提取用户信息
  extractUserInfo() {
    const userInfo = {
      name: '',
      username: '',
      avatar: '',
      bio: '',
      followers: '',
      following: '',
      totalLikes: '',
      douyinId: '',
      age: '',
      location: '',
      ipLocation: ''
    };

    try {
      // 提取用户名 - 优化支持名字后的图标
      const userInfoContainer = document.querySelector('[data-e2e="user-info"]');
      if (userInfoContainer) {
        const firstDiv = userInfoContainer.querySelector('div');
        if (firstDiv) {
          // 尝试多种可能的用户名选择器，支持包含图标的完整内容
          const nameSelectors = [
            'h1 span',
            'h1',
            '.a34DMvQe span',
            'span.j5WZzJdp span',
            'span'
          ];

          for (const selector of nameSelectors) {
            const nameElement = firstDiv.querySelector(selector);
            if (nameElement) {
              // 提取完整的HTML内容，包括文本和图标
              let nameContent = '';

              // 遍历所有子节点，提取文本和图标信息
              for (const node of nameElement.childNodes) {
                if (node.nodeType === Node.TEXT_NODE) {
                  nameContent += node.textContent.trim();
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                  if (node.tagName === 'IMG') {
                    // 如果是图片标签，添加图标描述
                    const alt = node.alt || '';
                    const title = node.title || '';
                    const iconDesc = alt || title || '认证';
                    nameContent += ` [${iconDesc}]`;
                  } else {
                    // 其他元素，提取文本内容
                    nameContent += node.textContent.trim();
                  }
                }
              }

              // 如果没有子节点，直接取文本内容
              if (!nameContent && nameElement.textContent.trim()) {
                nameContent = nameElement.textContent.trim();
              }

              if (nameContent) {
                userInfo.name = nameContent;
                break;
              }
            }
          }
        }
      }

      // 如果上面没找到，尝试其他选择器
      if (!userInfo.name) {
        const fallbackSelectors = [
          'h1.a34DMvQe span.j5WZzJdp span span span span span',
          'h1 span span span span span span',
          '[data-e2e="user-title"] span',
          '.user-info h1 span'
        ];

        for (const selector of fallbackSelectors) {
          const nameElement = document.querySelector(selector);
          if (nameElement) {
            // 同样处理包含图标的情况
            let nameContent = '';
            for (const node of nameElement.childNodes) {
              if (node.nodeType === Node.TEXT_NODE) {
                nameContent += node.textContent.trim();
              } else if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'IMG') {
                const iconDesc = node.alt || node.title || '认证';
                nameContent += ` [${iconDesc}]`;
              }
            }

            if (!nameContent && nameElement.textContent.trim()) {
              nameContent = nameElement.textContent.trim();
            }

            if (nameContent) {
              userInfo.name = nameContent;
              break;
            }
          }
        }
      }

      // 提取头像 - 排除当前登录用户头像（在a标签内），只匹配作者头像（不在a标签内），避免使用动态类名
      const avatarSelectors = [
        // 主要选择器：基于稳定属性和DOM结构，避免动态类名
        'div:not(a) [data-e2e="live-avatar"] img[alt*="头像"]',  // 不在a标签内的live-avatar头像
        'div:not(a) [data-e2e="live-avatar"] img',              // 备用：不在a标签内的live-avatar图片

        // 基于DOM结构的选择器
        'h1 + div + p + div img',                              // 结构选择器：用户名标题后的结构中的图片
        'div:not(a) span[role="listitem"] img[alt*="头像"]',   // 不在a标签内的角色头像
        '[data-e2e="user-info"] ~ div img',                   // 相邻选择器：用户信息旁边的图片

        // 基于稳定属性的选择器
        'div:not(a) [role="listitem"] img',                    // 不在a标签内的角色列表项图片
        '[data-e2e="user-info"] + div img',                   // 用户信息相邻元素中的图片

        // 通用备用选择器
        '.avatar-component-avatar-container:not(a *) img',      // 不在a标签内的头像容器
        '[data-e2e="user-avatar"] img',                        // user-avatar
        '.user-avatar img',                                     // 通用头像类
        '.avatar img'                                           // 简单头像类
      ];


      for (const selector of avatarSelectors) {
        const avatarElement = document.querySelector(selector);
        if (avatarElement && avatarElement.src && !avatarElement.src.includes('default')) {
          // 验证是否为有效的头像URL
          if (avatarElement.src.includes('douyinpic.com') ||
            avatarElement.src.includes('avatar') ||
            avatarElement.width >= 40) { // 头像通常有一定尺寸
            userInfo.avatar = avatarElement.src;
            break;
          }
        }
      }

      // 如果还是没找到头像，尝试从页面中的第一个大头像
      if (!userInfo.avatar) {
        const allAvatars = document.querySelectorAll('img[src*="avatar"], img[src*="head"], img[src*="douyinpic"]');
        for (const img of allAvatars) {
          if (img.width >= 40 && img.height >= 40 && !img.src.includes('default')) {
            userInfo.avatar = img.src;
            break;
          }
        }
      }

      // 提取抖音号 - 优化选择器策略
      if (!userInfo.douyinId) {
        // 方法1：基于DOM结构和文本内容查找
        const userInfoContainer = document.querySelector('[data-e2e="user-info"]');
        if (userInfoContainer) {
          // 查找包含"抖音号"文本的元素
          const allSpans = userInfoContainer.querySelectorAll('p span');
          for (const span of allSpans) {
            const text = span.textContent;
            if (text && text.includes('抖音号')) {
              const douyinMatch = text.match(/抖音号[：:]\s*([a-zA-Z0-9._]+)/);
              if (douyinMatch) {
                userInfo.douyinId = douyinMatch[1];
                userInfo.username = douyinMatch[1];
                break;
              }
            }
          }
        }

        // 方法2：备用选择器
        if (!userInfo.douyinId) {
          const douyinSelectors = [
            '[data-e2e="user-info"] p span:first-child', // 基于DOM结构
            'p span[class*="Ntumb"]', // 部分类名匹配
            'p span:contains("抖音号")', // 文本内容匹配
          ];

          for (const selector of douyinSelectors) {
            try {
              let element;
              if (selector.includes(':contains')) {
                // 手动实现contains功能
                const baseSelector = selector.split(':contains')[0];
                const searchText = selector.match(/:contains\("([^"]+)"\)/)?.[1];
                const elements = document.querySelectorAll(baseSelector);
                element = Array.from(elements).find(el => el.textContent.includes(searchText));
              } else {
                element = document.querySelector(selector);
              }

              if (element) {
                const text = element.textContent;
                const douyinMatch = text.match(/抖音号[：:]\s*([a-zA-Z0-9._]+)/);
                if (douyinMatch) {
                  userInfo.douyinId = douyinMatch[1];
                  userInfo.username = douyinMatch[1];
                  break;
                }
              }
            } catch (error) {
              console.warn('选择器失败:', selector, error);
            }
          }
        }
      }

      // 提取IP属地 - 优化选择器策略
      if (!userInfo.ipLocation) {
        const userInfoContainer = document.querySelector('[data-e2e="user-info"]');
        if (userInfoContainer) {
          // 查找包含"IP属地"文本的元素
          const allSpans = userInfoContainer.querySelectorAll('p span');
          for (const span of allSpans) {
            const text = span.textContent;
            if (text && text.includes('IP属地')) {
              const ipMatch = text.match(/IP属地[：:]\s*(.+)/);
              if (ipMatch) {
                userInfo.ipLocation = ipMatch[1];
                break;
              }
            }
          }
        }
      }

      // 提取年龄 - 优化选择器策略
      if (!userInfo.age) {
        const userInfoContainer = document.querySelector('[data-e2e="user-info"]');
        if (userInfoContainer) {
          // 查找包含"岁"的元素
          const allSpans = userInfoContainer.querySelectorAll('p span');
          for (const span of allSpans) {
            const text = span.textContent;
            if (text && text.includes('岁')) {
              const ageMatch = text.match(/(\d+)岁/);
              if (ageMatch) {
                userInfo.age = ageMatch[1];
                break;
              }
            }
          }
        }
      }

      // 提取关注数 - 优化选择器策略
      const followSelectors = [
        '[data-e2e="user-info-follow"] div:last-child', // 基于data-e2e属性
        '[data-e2e="user-info-follow"] .cIss_G7b', // 当前DOM结构
        '[data-e2e="user-info-follow"] div[class*="cIss"]', // 部分类名匹配
        '[data-e2e="user-info-follow"] div:nth-child(2)', // 基于DOM结构
      ];

      for (const selector of followSelectors) {
        const followElement = document.querySelector(selector);
        if (followElement && followElement.textContent.trim()) {
          userInfo.following = followElement.textContent.trim();
          break;
        }
      }

      // 提取粉丝数 - 优化选择器策略
      const fansSelectors = [
        '[data-e2e="user-info-fans"] div:last-child', // 基于data-e2e属性
        '[data-e2e="user-info-fans"] .cIss_G7b', // 当前DOM结构
        '[data-e2e="user-info-fans"] div[class*="cIss"]', // 部分类名匹配
        '[data-e2e="user-info-fans"] div:nth-child(2)', // 基于DOM结构
      ];

      for (const selector of fansSelectors) {
        const fansElement = document.querySelector(selector);
        if (fansElement && fansElement.textContent.trim()) {
          userInfo.followers = fansElement.textContent.trim();
          break;
        }
      }

      // 提取获赞数 - 优化选择器策略
      const likeSelectors = [
        '[data-e2e="user-info-like"] div:last-child', // 基于data-e2e属性
        '[data-e2e="user-info-like"] .cIss_G7b', // 当前DOM结构
        '[data-e2e="user-info-like"] div[class*="cIss"]', // 部分类名匹配
        '[data-e2e="user-info-like"] div:nth-child(2)', // 基于DOM结构
      ];

      for (const selector of likeSelectors) {
        const likeElement = document.querySelector(selector);
        if (likeElement && likeElement.textContent.trim()) {
          userInfo.totalLikes = likeElement.textContent.trim();
          break;
        }
      }

      // 提取个人简介 - 优化选择器策略
      if (!userInfo.bio) {
        const bioSelectors = [
          '[data-e2e="user-info"] + div span[class*="j5WZzJdp"]', // 基于DOM结构
          '.ZgdQUz2d span[class*="j5WZzJdp"]', // 当前DOM结构
          '[data-e2e="user-info"] ~ div span', // 兄弟元素选择
          '.user-bio span', // 语义化选择器
        ];

        for (const selector of bioSelectors) {
          const bioElement = document.querySelector(selector);
          if (bioElement && bioElement.textContent.trim()) {
            // 过滤掉可能的链接或其他非简介内容
            const bioText = bioElement.textContent.trim();
            if (bioText.length > 5 && !bioText.includes('http') && !bioText.includes('www.')) {
              userInfo.bio = bioText;
              break;
            }
          }
        }
      }

    } catch (error) {
      console.error('提取用户信息时出错:', error);
    }

    this.userInfo = userInfo;
    return userInfo;
  }

  // 提取作品列表
  extractVideoList() {
    // 找到用户作品列表容器
    const postListContainer = document.querySelector('[data-e2e="user-post-list"]');
    if (!postListContainer) {
      return [];
    }

    // 获取所有作品项（li元素）
    const videoItems = postListContainer.querySelectorAll('li');

    const videos = [];

    videoItems.forEach((li, index) => {
      try {
        const videoData = {
          id: '',
          title: '',
          author: this.userInfo?.name || '',
          authorUsername: this.userInfo?.username || '',
          authorAvatar: this.userInfo?.avatar || '',
          authorBio: this.userInfo?.bio || '',
          likes: '',
          coverUrl: '',
          videoUrl: '',
          isTop: false,
          extractedAt: new Date().toISOString()
        };

        // 提取视频ID和链接
        const linkElement = li.querySelector('a');
        if (linkElement && linkElement.href) {
          videoData.videoUrl = linkElement.href;

          // 从href中提取视频ID
          const match = linkElement.href.match(/\/video\/(\d+)/);
          if (match) {
            videoData.id = match[1];
          }
        }

        // 提取视频标题
        const titleElement = li.querySelector('a p');
        if (titleElement) {
          videoData.title = titleElement.textContent.trim();
        }

        // 提取封面图片
        const imgElement = li.querySelector('img');
        if (imgElement && imgElement.src) {
          videoData.coverUrl = imgElement.src;
        }

        // 检查是否为置顶视频
        const topElement = li.querySelector('.user-video-stats-tag');
        if (topElement && topElement.textContent.includes('置顶')) {
          videoData.isTop = true;
        }

        // 提取点赞数 - 改进的稳定方法
        const likeSelectors = [
          '.author-card-user-video-like',
          '.video-like-count',
          '.like-count',
          '[class*="like"]',
          '[data-e2e*="like"]'
        ];

        let likeCount = '';
        for (const selector of likeSelectors) {
          const likeContainer = li.querySelector(selector);
          if (likeContainer) {
            // 策略1: 查找spans中的数字
            const spans = likeContainer.querySelectorAll('span');
            for (let i = 0; i < spans.length; i++) {
              const spanText = spans[i].textContent.trim();
              if (/^\d+(\.\d+)?[\w万千]*$/.test(spanText)) {
                likeCount = spanText;
                break;
              }
            }

            // 策略2: 如果span没找到，从容器文本中提取
            if (!likeCount) {
              const allText = likeContainer.innerText || likeContainer.textContent || '';
              const numbers = allText.match(/\d+(\.\d+)?[\w万千]*/g);
              if (numbers && numbers.length > 0) {
                likeCount = numbers[numbers.length - 1];
              }
            }

            // 策略3: 查找任何包含数字的子元素
            if (!likeCount) {
              const allElements = likeContainer.querySelectorAll('*');
              for (const el of allElements) {
                const text = el.textContent.trim();
                if (/^\d+(\.\d+)?[\w万千]*$/.test(text)) {
                  likeCount = text;
                  break;
                }
              }
            }

            if (likeCount) break;
          }
        }

        // 直接使用原始点赞数字符串，不进行转换
        videoData.likes = likeCount;

        // 检查是否已经获取过
        if (videoData.id && !this.extractedVideoIds.has(videoData.id)) {
          this.extractedVideoIds.add(videoData.id);
          videos.push(videoData);
        } else if (videoData.id) {
          // console.log(`⚭️ 跳过重复视频: ${videoData.id}`);
        }

      } catch (error) {
        console.error(`提取第${index + 1}个视频时出错:`, error);
      }
    });

    return videos;
  }

  // 开始获取
  startExtraction(maxCount = 100) {
    if (this.isExtracting) {
      return;
    }

    if (!this.isUserPage()) {
      return;
    }

    // 检查扩展上下文是否有效
    if (!checkExtensionContext()) {
      handleExtensionContextError('扩展上下文失效');
      return;
    }

    this.isExtracting = true;
    this.maxExtractCount = maxCount;
    this.currentExtractCount = 0;
    this.extractedData = [];
    this.extractedVideoIds.clear();

    // 更新按钮状态
    this.updateButtonState();

    // 先提取用户信息
    this.extractUserInfo();

    // 立即获取一次
    this.extractAndUpdate();

    // 开始自动滚动
    this.startAutoScroll();

    this.showNotification(`开始获取 ${this.userInfo?.name || '用户'} 的作品，目标${maxCount}条`);
  }

  // 获取并更新数据
  extractAndUpdate() {
    if (!this.isExtracting) return;

    const newVideos = this.extractVideoList();

    if (newVideos.length > 0) {
      this.extractedData.push(...newVideos);
      this.currentExtractCount += newVideos.length;

      this.extractAndUpdate();

      // 检查是否达到目标数量
      if (this.currentExtractCount >= this.maxExtractCount) {
        this.stopExtraction();
        return;
      }
    } else {
      // console.log('⚠️ 本次未发现新视频');
    }
  }

  // 自动滚动
  startAutoScroll() {
    let scrollCount = 0;
    let noNewDataCount = 0;

    // 智能识别滚动容器 - 参考混淆代码的容器识别策略
    const findScrollContainer = () => {
      // 优先查找抖音特定的滚动容器
      const containers = [
        // 用户主页的滚动容器
        '.parent-route-container.route-scroll-container',
        '.parent-route-container.bX97FWk8.route-scroll-container.h5AVrOfS',
        '.child-route-container.route-scroll-container.h5AVrOfS',
        // 搜索页面的滚动容器
        'div#search-result-container>div[style*="display: block"]:nth-child(1)>div#waterFallScrollContainer',
        // 发现页面的滚动容器
        '[data-e2e="scroll-list"]',
        // 通用容器
        '[data-e2e="user-post-list"]'
      ];

      for (const selector of containers) {
        const container = document.querySelector(selector);
        if (container) {
          return container;
        }
      }

      return null;
    };

    const scrollContainer = findScrollContainer();

    this.autoScrollInterval = setInterval(() => {
      if (!this.isExtracting) {
        clearInterval(this.autoScrollInterval);
        return;
      }

      scrollCount++;

      // 获取当前滚动状态
      let currentScrollTop, scrollHeight, clientHeight;

      if (scrollContainer) {
        currentScrollTop = scrollContainer.scrollTop;
        scrollHeight = scrollContainer.scrollHeight;
        clientHeight = scrollContainer.clientHeight;
      } else {
        currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
        scrollHeight = document.body.scrollHeight;
        clientHeight = window.innerHeight;
      }

      // 智能滚动策略 - 参考混淆代码的分步滚动
      const scrollStep = 1000; // 每次滚动1000px
      const steps = 10; // 分10步滚动
      const stepDelay = 50; // 每步延迟50ms

      if (scrollCount % 3 === 0) {
        // 每3次滚动使用渐进式滚动，更好地触发懒加载
        let currentStep = 0;
        const stepInterval = setInterval(() => {
          if (currentStep >= steps) {
            clearInterval(stepInterval);
            return;
          }

          const targetScroll = Math.min(scrollHeight, currentScrollTop + scrollStep * (currentStep + 1) / steps);

          if (scrollContainer) {
            scrollContainer.scrollTop = targetScroll;
          } else {
            window.scrollTo(0, targetScroll);
          }

          currentStep++;
        }, stepDelay);
      } else {
        // 直接滚动到底部
        if (scrollContainer) {
          scrollContainer.scrollTop = scrollHeight;
        } else {
          window.scrollTo(0, scrollHeight);
        }
      }

      // 检查是否到达底部
      const isAtBottom = scrollContainer
        ? (scrollContainer.scrollTop + scrollContainer.clientHeight >= scrollContainer.scrollHeight - 10)
        : (window.innerHeight + window.pageYOffset >= document.body.offsetHeight - 10);

      if (isAtBottom) {
        // console.log('📍 已到达页面底部');
      }

      // 等待内容加载并检查数据更新
      setTimeout(() => {
        const beforeCount = this.currentExtractCount;

        // 检查是否有新内容加载
        let newScrollHeight;
        if (scrollContainer) {
          newScrollHeight = scrollContainer.scrollHeight;
        } else {
          newScrollHeight = document.body.scrollHeight;
        }

        const hasNewContent = newScrollHeight > scrollHeight;

        // 检查是否有"暂时没有更多了"的提示
        const noMoreTips = document.querySelectorAll('*');
        let hasNoMoreTip = false;
        for (const element of noMoreTips) {
          if (element.textContent && element.textContent.includes('暂时没有更多了')) {
            hasNoMoreTip = true;
            break;
          }
        }

        this.extractAndUpdate();

        // 延长检查时间，确保数据提取完成
        setTimeout(() => {
          const newDataCount = this.currentExtractCount - beforeCount;

          if (newDataCount === 0) {
            noNewDataCount++;

            // 如果检测到"没有更多"提示，或者到达底部且无新内容，提前停止
            if (hasNoMoreTip || (isAtBottom && !hasNewContent && noNewDataCount >= 2)) {
              this.stopExtraction();
              return;
            }

            // 连续5次无新数据，停止获取（减少等待时间）
            if (noNewDataCount >= 5) {
              this.stopExtraction();
              return;
            }
          } else {
            noNewDataCount = 0;
          }

          // 检查是否达到目标数量
          if (this.currentExtractCount >= this.maxExtractCount) {
            this.stopExtraction();
          }
        }, 800); // 减少检查延迟到800ms
      }, 1500); // 减少滚动后等待时间到1.5秒

    }, 2500); // 减少滚动间隔到2.5秒
  }

  // 停止获取
  stopExtraction() {
    if (!this.isExtracting) return;

    this.isExtracting = false;

    if (this.autoScrollInterval) {
      clearInterval(this.autoScrollInterval);
      this.autoScrollInterval = null;
    }

    this.updateButtonState();
    this.sendFinalData();

    const message = `获取完成！共获取 ${this.currentExtractCount} 条作品数据`;
    this.showNotification(message);

    // 确保floatingButton状态正确更新
    if (this.floatingButton) {
      this.floatingButton.textContent = '获取用户作品';
      this.floatingButton.style.background = '#28a745';
    }
  }

  // 发送最终数据
  sendFinalData() {
    // 构建符合图片中JSON结构的数据
    const structuredData = {
      domain: "douyin",
      type: "profile",
      data: {
        userId: this.userInfo?.douyinId || '',
        username: this.userInfo?.name || '',
        avatar: this.userInfo?.avatar || '',
        description: this.userInfo?.bio || '',
        age: this.userInfo?.age || '',
        gender: '',
        constellation: '',
        location: this.userInfo?.ipLocation || '',
        ipLocation: this.userInfo?.ipLocation || '',
        following: this.userInfo?.following || '',
        followers: this.userInfo?.followers || '',
        likes: this.userInfo?.totalLikes || '',
        postCount: this.extractedData.length.toString(),
        verified: '',
        videos: this.extractedData.map(video => ({
          title: video.title,
          cover: video.coverUrl,
          author: video.author,
          authorID: this.userInfo?.douyinId || '',
          authorLink: '',
          authorAvatar: this.userInfo?.avatar || '',
          link: video.videoUrl,
          likes: video.likes,
          isTop: video.isTop,
          duration: '',
          publishTime: ''
        }))
      }
    };

    // 简化版数据用于内部处理
    const finalData = {
      videos: this.extractedData,
      userInfo: this.userInfo,
      extractedAt: new Date().toISOString(),
      url: window.location.href,
      totalCount: this.extractedData.length,
      pageType: 'profile',
      structuredData: structuredData // 添加结构化数据
    };

    // 保存到本地存储 - 按用户去重
    try {
      chrome.storage.local.get(['douyinData'], (result) => {
        if (chrome.runtime.lastError) {
          console.error('Storage access failed:', chrome.runtime.lastError);
          handleExtensionContextError('存储访问失败');
          return;
        }

        const existingData = result.douyinData || [];

        // 查找是否已存在相同用户的数据
        const userKey = this.userInfo?.douyinId || this.userInfo?.name || window.location.href;
        const existingIndex = existingData.findIndex(item => {
          const itemUserKey = item.userInfo?.douyinId || item.userInfo?.name || item.url;
          return itemUserKey === userKey;
        });

        if (existingIndex !== -1) {
          // 如果已存在，替换旧数据
          existingData[existingIndex] = finalData;
        } else {
          // 如果不存在，添加新数据
          existingData.push(finalData);
        }

        chrome.storage.local.set({ douyinData: existingData }, () => {
          if (chrome.runtime.lastError) {
            console.error('Storage set failed:', chrome.runtime.lastError);
            handleExtensionContextError('数据保存失败');
            return;
          }

          // 检查扩展上下文是否仍然有效
          if (!checkExtensionContext()) {
            console.warn('Extension context invalidated during save');
            return;
          }

          // 发送EXTRACTION_COMPLETE消息给popup
          chrome.runtime.sendMessage({
            type: 'EXTRACTION_COMPLETE',
            data: finalData
          }).catch(error => {
            console.warn('发送最终数据失败:', error);
          });

          // 同时发送EXTRACTION_FINISHED消息，明确通知popup获取已完成，需要更新按钮状态
          chrome.runtime.sendMessage({
            type: 'EXTRACTION_FINISHED',
            success: true,
            message: `获取完成! 共获取 ${finalData.totalCount} 条数据`
          }).catch(error => {
            console.warn('发送获取完成消息失败:', error);
          });
        });
      });
    } catch (error) {
      console.error('Extension context error:', error);
      handleExtensionContextError('扩展上下文异常');
    }
  }

  // 更新按钮状态
  updateButtonState() {
    if (this.floatingButton) {
      if (this.isExtracting) {
        this.floatingButton.textContent = '停止获取';
        this.floatingButton.style.background = '#dc3545';
      } else {
        this.floatingButton.textContent = '获取用户作品';
        this.floatingButton.style.background = '#28a745';
      }
    }
  }

  // 显示通知
  showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 80px;
      right: 20px;
      z-index: 10000;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 15px;
      border-radius: 10px;
      font-size: 14px;
      max-width: 300px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // 显示popup提示
  showPopupHint() {
    const hint = document.createElement('div');
    hint.style.cssText = `
      position: fixed;
      top: 120px;
      right: 20px;
      z-index: 10000;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px 20px;
      border-radius: 12px;
      font-size: 13px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      max-width: 280px;
      border: 2px solid rgba(255,255,255,0.2);
      backdrop-filter: blur(10px);
    `;

    hint.innerHTML = `
      <div style="display: flex; align-items: center; margin-bottom: 8px;">
        <span style="font-size: 16px; margin-right: 8px;">💡</span>
        <strong>提示</strong>
      </div>
      <div style="line-height: 1.4;">
        点击浏览器工具栏的扩展图标<br>
        可以查看抓取进度和数据详情
      </div>
    `;

    document.body.appendChild(hint);

    // 5秒后自动消失
    setTimeout(() => {
      if (hint.parentNode) {
        hint.parentNode.removeChild(hint);
      }
    }, 5000);
  }

  // 清除历史数据
  clearHistory(clearAll = true) {
    if (!checkExtensionContext()) {
      handleExtensionContextError('扩展上下文失效');
      return;
    }

    if (clearAll) {
      // 清除所有数据
      chrome.storage.local.remove(['douyinUserData', 'douyinVideoDetails'], () => {
        this.showNotification('已清除所有历史数据');
      });
    } else {
      // 只清除用户数据
      chrome.storage.local.remove(['douyinUserData'], () => {
        this.showNotification('已清除用户数据');
      });
    }
  }

  // 解析数字（支持K、M、W等单位）
  parseCount(countStr) {
    if (!countStr || typeof countStr !== 'string') return 0;

    // 清理字符串，移除空格和逗号
    const cleanStr = countStr.toLowerCase().replace(/[,\s]/g, '');

    // 处理纯数字
    if (/^\d+$/.test(cleanStr)) {
      const result = parseInt(cleanStr);
      return result;
    }

    // 处理带单位的数字
    let multiplier = 1;
    let numberPart = cleanStr;

    // 万/w 单位
    if (cleanStr.includes('w') || cleanStr.includes('万')) {
      multiplier = 10000;
      numberPart = cleanStr.replace(/[w万]/g, '');
    }
    // 千/k 单位  
    else if (cleanStr.includes('k') || cleanStr.includes('千')) {
      multiplier = 1000;
      numberPart = cleanStr.replace(/[k千]/g, '');
    }
    // 百万/m 单位
    else if (cleanStr.includes('m') || cleanStr.includes('百万')) {
      multiplier = 1000000;
      numberPart = cleanStr.replace(/[m百万]/g, '');
    }
    // 亿 单位
    else if (cleanStr.includes('亿')) {
      multiplier = 100000000;
      numberPart = cleanStr.replace(/亿/g, '');
    }

    // 解析数字部分（支持小数）
    const number = parseFloat(numberPart);
    if (isNaN(number)) {
      return 0;
    }

    const result = Math.floor(number * multiplier);
    return result;
  }
}

// 初始化获取器和状态管理器
const extractor = new DouyinUserPageExtractor();
const pageStateManager = new PageStateManager();

// 将对象暴露为全局对象，使popup可以直接访问
window.extractor = extractor;
window.pageStateManager = pageStateManager;



// 监听来自popup的消息
if (checkExtensionContext()) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    // 视频详情页消息处理 - 使用统一检测逻辑
    if (request.action === 'extractVideoDetail') {
      const currentPageType = pageStateManager.detectPageType();

      if (currentPageType === 'videoDetail') {
        return true; // 转发给video-detail.js处理
      } else {
        sendResponse({
          success: false,
          error: '未检测到视频播放器，请在视频详情页使用此功能'
        });
        return true;
      }
    }

    // 停止视频详情获取 - 直接转发
    if (request.action === 'stopVideoDetailExtraction') {
      return true; // 转发给video-detail.js处理
    }

    // 视频详情页类型检测 - 直接转发
    if (request.action === 'getVideoDetailPageType') {
      return true; // 转发给video-detail.js处理
    }

    // 新增：页面状态查询消息
    if (request.action === 'getPageState') {
      const state = pageStateManager.getCurrentState();
      sendResponse({
        success: true,
        ...state
      });
      return true;
    }

    // 新增：强制重新检测页面类型
    if (request.action === 'forceRedetect') {
      pageStateManager.forceRedetect();
      sendResponse({
        success: true,
        message: '已强制重新检测页面类型'
      });
      return true;
    }

    // 使用PageStateManager的统一检测逻辑
    const currentPageType = pageStateManager.detectPageType();

    // 用户页面消息
    const userPageMessages = ['getUserPageType', 'extractUserData', 'stopAutoExtraction'];
    const isUserPageMessage = userPageMessages.includes(request.action);

    // 如果是用户页面且是用户页面相关消息 - 优先处理
    if (currentPageType === 'user' && isUserPageMessage) {
      switch (request.action) {
        case 'extractUserData':
        case 'extractData':
        case 'startAutoExtraction':
          const maxCount = request.maxCount || request.config?.maxScrolls || 100;
          extractor.startExtraction(maxCount);
          sendResponse({ success: true, message: `开始获取，目标: ${maxCount} 条` });
          break;

        case 'stopAutoExtraction':
          extractor.stopExtraction();
          sendResponse({ success: true });
          break;

        case 'getUserPageType':
          sendResponse({
            success: true,
            isUserPage: true,
            pageType: 'user',
            buttonConfig: {
              text: extractor.isExtracting ? '停止获取' : '获取用户作品',
              color: extractor.isExtracting ? '#dc3545' : '#28a745',
              action: 'extractUserData'
            }
          });
          break;
      }

      return true;
    }

    // 其他通用消息
    switch (request.action) {
      case 'getPageStats':
        sendResponse({
          pageType: isUserPage ? 'profile' : 'other',
          videoCount: document.querySelectorAll('[data-e2e="user-post-list"] li').length,
          currentUrl: window.location.href,
          pageTitle: document.title
        });
        break;

      case 'getPageType':
        sendResponse({
          success: true,
          pageType: isUserPage ? 'profile' : 'other',
          buttonConfig: {
            text: extractor.isExtracting ? '停止获取' : '获取用户作品',
            color: extractor.isExtracting ? '#dc3545' : '#28a745',
            action: 'extractUserVideos'
          }
        });
        break;

      case 'getPageInfo':
        if (isUserPage) {
          sendResponse({
            success: true,
            pageType: 'profile',
            buttonConfig: {
              text: extractor.isExtracting ? '停止获取' : '获取用户作品',
              color: extractor.isExtracting ? '#dc3545' : '#28a745',
              action: 'extractUserVideos'
            }
          });
        } else {
          return false;
        }
        break;

      case 'clearExtractionHistory':
        extractor.clearHistory();
        sendResponse({ success: true });
        break;

      default:
        sendResponse({ success: false, error: '未知操作' });
    }

    return true;
  });

  // 初始化页面状态管理器（在抖音页面）
  if (window.location.hostname.includes('douyin.com')) {
    // 立即初始化，然后定期检查
    pageStateManager.init();

    // 等待页面加载完成后再次检查
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          pageStateManager.forceRedetect();
        }, 1000);
      });
    } else {
      // 页面已加载完成，延迟检查以确保DOM稳定
      setTimeout(() => {
        pageStateManager.forceRedetect();
      }, 1000);
    }

    // 定期检查页面状态（每5秒）
    setInterval(() => {
      pageStateManager.forceRedetect();
    }, 5000);
  } else {
    console.warn('Extension context not available, skipping message listener setup');
  }
}