package org.dromara.member.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.member.service.IPointsService;
import org.springframework.stereotype.Component;

/**
 * 每日积分重置定时任务
 * 每日0点为所有等级为0的注册会员重置200点资源点，有效期1天
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Component
@RequiredArgsConstructor
@JobExecutor(name = "dailyPointsResetTask")
public class DailyPointsResetTask {

    private final IPointsService pointsService;

    /**
     * 执行每日积分重置任务
     */
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        try {
            SnailJobLog.REMOTE.info("开始执行每日积分重置任务");
            log.info("开始执行每日积分重置任务");

            // 调用积分服务执行重置
            pointsService.dailyPointsReset();

            SnailJobLog.REMOTE.info("每日积分重置任务执行完成");
            log.info("每日积分重置任务执行完成");

            return ExecuteResult.success("每日积分重置任务执行成功");
        } catch (Exception e) {
            String errorMsg = "每日积分重置任务执行失败: " + e.getMessage();
            SnailJobLog.REMOTE.error(errorMsg, e);
            log.error(errorMsg, e);
            return ExecuteResult.failure(errorMsg);
        }
    }
}
