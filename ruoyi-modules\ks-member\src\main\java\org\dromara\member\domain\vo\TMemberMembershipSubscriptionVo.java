package org.dromara.member.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会员套餐订阅记录视图对象 t_member_membership_subscription
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@ExcelIgnoreUnannotated
public class TMemberMembershipSubscriptionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID")
    private Long goodsId;

    /**
     * 会员类型
     */
    @ExcelProperty(value = "会员类型")
    private String membershipType;

    /**
     * 开始日期
     */
    @ExcelProperty(value = "开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @ExcelProperty(value = "结束日期")
    private Date endDate;

    /**
     * 是否自动续费：0=否，1=是
     */
    @ExcelProperty(value = "是否自动续费")
    private Integer autoRenew;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 支付金额
     */
    @ExcelProperty(value = "支付金额")
    private Long paymentAmount;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;
}

