package org.dromara.member.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会员权益生效记录视图对象 t_member_benefit_grants
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@ExcelIgnoreUnannotated
public class TMemberBenefitGrantsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 权益配置ID
     */
    @ExcelProperty(value = "权益配置ID")
    private Long benefitConfigId;

    /**
     * 旧等级
     */
    @ExcelProperty(value = "旧等级")
    private Integer oldLevel;

    /**
     * 新等级
     */
    @ExcelProperty(value = "新等级")
    private Integer newLevel;

    /**
     * 授予时间
     */
    @ExcelProperty(value = "授予时间")
    private Date grantDate;

    /**
     * 生效时间
     */
    @ExcelProperty(value = "生效时间")
    private Date effectiveDate;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    private Date expiryDate;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;
}
