package org.dromara.member.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会员充值记录视图对象 t_member_recharge_record
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@ExcelIgnoreUnannotated
public class TMemberRechargeRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 充值商品ID
     */
    @ExcelProperty(value = "充值商品ID")
    private Long goodsId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private String orderId;

    /**
     * 充值金额（分）
     */
    @ExcelProperty(value = "充值金额")
    private Long rechargeAmount;

    /**
     * 获得积分数
     */
    @ExcelProperty(value = "获得积分数")
    private Long pointsAmount;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式")
    private String paymentMethod;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "支付时间")
    private Date rechargeTime;

    /**
     * 支付状态
     */
    @ExcelProperty(value = "支付状态")
    private String status;

    /**
     * 交易流水号
     */
    @ExcelProperty(value = "交易流水号")
    private String transactionId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;
}
