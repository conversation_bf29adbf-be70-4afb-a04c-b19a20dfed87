// 巨量引擎数据获取器弹窗管理器

class OceanEnginePopupManager {
  constructor() {
    this.currentTab = null;
    this.currentPageType = "unknown";
    this.rankingType = null;
    this.isExtracting = false;
    this.permissionManager = null; // 统一权限管理器

    this.extractedData = {
      hot_content: {
        short_video: [], // 短视频榜
        picture_content: [], // 图文榜
        product_card: [], // 商品卡榜
      },
      aweme: {
        Retailers: [], // 电商行业榜
        App: [], // 应用下载榜
        Clue: [], // 线索收集榜
        ShortVideo: [], // 短视频主题榜
      },
      ranking: {
        hot_word: [], // 热词榜
        hot_topic: [], // 热点榜
        marketing_hot_pots: [], // 营销热点榜
      },
    };
    this.settings = {
      autoExtractInfluencers: true,
      autoExtractRankings: true,
      enableAutoScroll: true,
      extractDelay: 2000,
      maxExtractCount: 100,
      scrollDelay: 3000,
      includeTimestamp: true,
      includePageInfo: true,
      includeRankInfo: true,
      exportFormat: "json",
      detectYuntuComponents: true,
      detectRankingPages: true,
      multidimensionalTableUrl: "",
      confirmBeforeBilling: true,
      get serverUrl() {
        return window.pluginConfig?.getServerBaseUrl() || "https://ksun.chat:7013";
      }
    };

    this.init();
  }

  async init() {
    await this.loadSettings();
    await this.initializePermissionManagement();
    this.setupEventListeners();
    this.setupTabs();
    this.checkCurrentTab();
    this.setupMessageListener();
    await this.loadStoredData();
    this.updateAllDisplays();
    this.setupImageErrorHandlers();
    this.setupUnifiedPermissionUI(); // 设置统一权限管理UI
  }

  // 设置图片错误处理器
  setupImageErrorHandlers() {
    // 使用事件委托为所有图片添加错误处理
    document.addEventListener(
      "error",
      (event) => {
        const target = event.target;
        // 只处理图片元素
        if (target.tagName === "IMG") {
          const parent = target.parentNode;
          const errorText = target.getAttribute("data-error-text") || "无图片";
          if (parent) {
            parent.classList.add("no-image");
            parent.textContent = errorText;
          }
          target.remove();
        }
      },
      true
    ); // 使用捕获阶段以确保能捕获到所有图片错误
  }

  // 初始化权限管理 - 使用billing管理器
  async initializePermissionManagement() {
    try {
      console.log('巨量引擎页面权限管理：使用billing管理器');

      // 直接使用billing管理器
      if (window.billingManager) {
        await window.billingManager.initialize();
        this.permissionManager = window.billingManager;
        console.log('billing管理器初始化成功');
      } else {
        console.error('billing管理器未找到');
      }
    } catch (error) {
      console.error('权限管理初始化失败:', error);
    }
  }

  // 设置事件监听器
  setupEventListeners() {
    // 获取按钮
    document.getElementById("extractBtn").addEventListener("click", () => {
      this.startExtraction();
    });

    // 停止获取按钮
    const stopBtn = document.getElementById("stopBtn");
    if (stopBtn) {
      stopBtn.addEventListener("click", () => {
        this.stopExtraction();
      });
    }



    // 清空图文榜按钮
    const clearPictureContentBtn = document.getElementById(
      "clearPictureContentBtn"
    );
    if (clearPictureContentBtn) {
      clearPictureContentBtn.addEventListener("click", () => {
        this.clearPictureContentData();
      });
    }

    // 保存多维表格URL按钮
    const saveTableUrlBtn = document.getElementById("saveTableUrlBtn");
    if (saveTableUrlBtn) {
      saveTableUrlBtn.addEventListener("click", () => {
        this.saveTableUrl();
      });
    }

    // 榜单类型切换事件
    document.querySelectorAll(".type-tab").forEach((tab) => {
      tab.addEventListener("click", (event) => {
        this.switchRankingType(event.target.dataset.type);
      });
    });

    // 数据操作按钮的事件委托
    document.addEventListener("click", async (event) => {
      const button = event.target.closest("button[data-action]");
      if (!button) return;

      const action = button.dataset.action;
      const category = button.dataset.category;
      const format = button.dataset.format;
      const target = button.dataset.target;

      switch (action) {
        case "export":
          this.exportCategoryData(category, format);
          break;
        case "clear":
          await this.clearCategoryData(category);
          break;
        case "export-all":
          this.exportAllData(format);
          break;
        case "clear-all":
          this.clearAllData();
          break;
        case "toggle":
          this.toggleDataList(target);
          break;
      }
    });

    // 设置项监听
    this.setupSettingsListeners();
  }

  // 设置标签页
  setupTabs() {
    const tabBtns = document.querySelectorAll(".tab-btn");
    const tabContents = document.querySelectorAll(".tab-content");

    tabBtns.forEach((btn) => {
      btn.addEventListener("click", () => {
        const targetTab = btn.dataset.tab;

        // 移除所有活动状态
        tabBtns.forEach((b) => b.classList.remove("active"));
        tabContents.forEach((c) => c.classList.remove("active"));

        // 激活当前标签
        btn.classList.add("active");
        document.getElementById(targetTab).classList.add("active");
      });
    });
  }

  // 检查当前标签页
  async checkCurrentTab(retryCount = 0) {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      this.currentTab = tab;

      // 检查是否为巨量引擎页面
      if (!tab.url || !tab.url.includes("oceanengine.com")) {
        this.updatePageStatus({
          pageType: "unknown",
          itemCount: 0,
          currentUrl: tab.url || "",
          pageTitle: "请在巨量引擎页面使用此扩展",
        });
        return;
      }

      // 发送消息获取页面状态
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: "getOceanEnginePageStats",
      });

      if (response) {
        this.updatePageStatus(response);
      } else {
        // 页面可能还没有加载完成，显示默认状态
        this.updatePageStatus({
          pageType: "unknown",
          itemCount: 0,
          currentUrl: tab.url || "",
          pageTitle: tab.title || "未知页面",
        });
      }
    } catch (error) {
      console.error("检查当前标签页失败:", error);

      // 如果是连接错误且重试次数少于3次，则延迟重试
      if (
        error.message.includes("Could not establish connection") &&
        retryCount < 3
      ) {
        setTimeout(() => {
          this.checkCurrentTab(retryCount + 1);
        }, 1000 * (retryCount + 1)); // 递增延迟：1s, 2s, 3s
        return;
      }

      // 重试失败或其他错误，显示错误状态
      this.updatePageStatus({
        pageType: "unknown",
        itemCount: 0,
        currentUrl: this.currentTab?.url || "",
        pageTitle:
          retryCount >= 3 ? "Content脚本加载失败，请刷新页面" : "未知页面",
      });
    }
  }

  // 更新页面状态
  updatePageStatus(stats) {
    const pageTypeElement = document.getElementById("pageType");
    const statusDescElement = document.getElementById("statusDescription");
    const itemCountElement = document.getElementById("itemCount");

    if (stats.pageType === "ranking") {
      pageTypeElement.textContent = "巨量引擎榜单页面";
      pageTypeElement.className = "page-type ranking";
      statusDescElement.textContent = "检测到榜单数据，可以开始获取";
      this.rankingType = stats.rankingType;
    } else if (
      stats.currentUrl &&
      stats.currentUrl.includes("oceanengine.com")
    ) {
      pageTypeElement.textContent = "巨量引擎页面";
      pageTypeElement.className = "page-type oceanengine";
      statusDescElement.textContent = "请前往榜单页面开始获取";
    } else {
      pageTypeElement.textContent = "非巨量引擎页面";
      pageTypeElement.className = "page-type unknown";
      statusDescElement.textContent = "请在巨量引擎榜单页面使用";
    }

    if (itemCountElement) {
      itemCountElement.textContent = stats.itemCount || 0;
    }

    this.currentPageType = stats.pageType;
  }

  // 开始获取
  async startExtraction() {
    if (this.isExtracting) {
      this.showNotification("获取正在进行中", "warning");
      return;
    }

    // 新增：获取开始时自动切换tab
    if (this.rankingType && typeof this.rankingType.category === "string") {
      this.switchRankingType(this.rankingType.category);
    }

    // 修改检测逻辑，支持更多的页面类型情况
    if (!this.currentTab.url.includes("oceanengine.com")) {
      this.showNotification("请在巨量引擎网站使用此功能", "error");
      return;
    }

    // 发送消息验证页面类型
    try {
      // 尝试发送消息前先检查标签页是否有效
      if (!this.currentTab || !this.currentTab.id) {
        this.showNotification(
          "无法获取当前标签页信息，请刷新页面后重试",
          "error"
        );
        return;
      }

      const pageInfo = await chrome.tabs
        .sendMessage(this.currentTab.id, {
          action: "getOceanEnginePageInfo",
        })
        .catch((error) => {
          // 捕获连接错误
          console.error("发送消息失败:", error);
          throw new Error("无法与页面建立连接，请刷新页面后重试");
        });

      if (
        !pageInfo ||
        !pageInfo.success ||
        pageInfo.pageInfo.subType !== "ranking"
      ) {
        this.showNotification("请在巨量引擎榜单页面使用此功能", "error");
        return;
      }

      // 新增：用最新的榜单类型切换tab，并更新this.rankingType
      if (
        pageInfo.pageInfo.rankingType &&
        typeof pageInfo.pageInfo.rankingType.category === "string"
      ) {
        this.rankingType = pageInfo.pageInfo.rankingType;
        this.switchRankingType(this.rankingType.category);
      }
    } catch (error) {
      console.error("页面检测失败:", error);

      // 提供更具体的错误信息
      let errorMessage = "页面检测失败";
      if (error.message.includes("Could not establish connection")) {
        errorMessage = "无法与页面建立连接，内容脚本可能未加载";
      } else if (error.message) {
        errorMessage = error.message;
      }

      this.showNotification(`${errorMessage}，请刷新页面后重试`, "error");
      return;
    }

    try {
      this.isExtracting = true;
      this.updateExtractButton(true);
      this.showProgress(true);

      const maxCount =
        parseInt(document.getElementById("maxExtractCount").value) || 100;

      // 发送开始获取消息
      const response = await chrome.tabs
        .sendMessage(this.currentTab.id, {
          action: "startOceanEngineExtraction",
          maxCount: maxCount,
        })
        .catch((error) => {
          // 捕获连接错误
          console.error("发送获取命令失败:", error);
          throw new Error("无法与页面建立连接，请刷新页面后重试");
        });

      if (response && response.success) {
        this.showNotification("开始获取榜单数据...", "info");
        this.updateProgress("开始获取...", 0, maxCount);
      } else {
        throw new Error("启动获取失败，内容脚本未返回成功响应");
      }
    } catch (error) {
      console.error("启动获取失败:", error);
      this.showNotification(`启动获取失败: ${error.message}`, "error");
      this.isExtracting = false;
      this.updateExtractButton(false);
      this.showProgress(false);
    }
  }

  // 停止获取
  async stopExtraction() {
    if (!this.isExtracting) {
      return;
    }

    try {
      // 检查标签页是否有效
      if (!this.currentTab || !this.currentTab.id) {
        throw new Error("无法获取当前标签页信息");
      }

      const response = await chrome.tabs
        .sendMessage(this.currentTab.id, {
          action: "stopOceanEngineExtraction",
        })
        .catch((error) => {
          // 捕获连接错误
          console.error("发送停止命令失败:", error);
          throw new Error("无法与页面建立连接");
        });

      this.isExtracting = false;
      this.updateExtractButton(false);
      this.showProgress(false);
      this.showNotification("获取已停止", "info");
    } catch (error) {
      console.error("停止获取失败:", error);

      // 提供更具体的错误信息
      let errorMessage = "停止获取失败";
      if (error.message.includes("Could not establish connection")) {
        errorMessage = "无法与页面建立连接，内容脚本可能未加载";
      } else if (error.message) {
        errorMessage = error.message;
      }

      this.showNotification(`${errorMessage}，请刷新页面后重试`, "error");

      // 即使出错也重置状态
      this.isExtracting = false;
      this.updateExtractButton(false);
      this.showProgress(false);
    }
  }



  // 更新获取按钮状态
  updateExtractButton(isExtracting) {
    const extractBtn = document.getElementById("extractBtn");
    const stopBtn = document.getElementById("stopBtn");

    if (isExtracting) {
      extractBtn.style.display = "none";
      stopBtn.style.display = "block";
    } else {
      extractBtn.style.display = "block";
      stopBtn.style.display = "none";
    }
  }

  // 显示/隐藏进度
  showProgress(show) {
    const progressDisplay = document.getElementById("progressDisplay");
    progressDisplay.style.display = show ? "block" : "none";
  }

  // 更新进度
  updateProgress(text, current, total) {
    const progressText = document.getElementById("progressText");
    const progressCount = document.getElementById("progressCount");
    const progressFill = document.getElementById("progressFill");

    if (progressText) progressText.textContent = text;
    if (progressCount) progressCount.textContent = `${current}/${total}`;

    if (progressFill && total > 0) {
      const percentage = Math.min((current / total) * 100, 100);
      progressFill.style.width = `${percentage}%`;
    }
  }

  // 设置消息监听器
  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.action) {
        case "oceanEngineProgress":
          this.handleProgressUpdate(message.data);
          break;
        case "oceanEngineComplete":
          this.handleExtractionComplete(message.data);
          break;
        case "oceanEngineError":
          this.handleExtractionError(message.error);
          break;
      }

      sendResponse({ received: true });
    });
  }

  // 处理进度更新
  handleProgressUpdate(data) {
    this.updateProgress(data.status, data.extractCount, data.maxCount);

    // 显示当前页面信息
    if (data.currentPage) {
      const statusText = `${data.status} (第${data.currentPage}页)`;
      document.getElementById("progressText").textContent = statusText;
    }
  }

  // 处理获取完成
  handleExtractionComplete(data) {
    this.isExtracting = false;
    this.updateExtractButton(false);
    this.showProgress(false);

    // 添加新数据
    if (data.extractedData) {
      this.addNewData(data.extractedData);
    }



    this.showNotification(
      `获取完成！共获取 ${data.extractCount} 条数据`,
      "success"
    );
    this.updateAllDisplays();
    this.saveDataToStorage();
  }



  // 处理获取错误
  handleExtractionError(error) {
    this.isExtracting = false;
    this.updateExtractButton(false);
    this.showProgress(false);
    this.showNotification(`获取出错: ${error}`, "error");
  }

  // 添加新数据
  addNewData(newData) {
    for (const [category, types] of Object.entries(newData)) {
      if (this.extractedData[category]) {
        for (const [type, items] of Object.entries(types)) {
          if (this.extractedData[category][type] && Array.isArray(items)) {
            // 特殊处理短视频榜、图文榜和线索收集榜数据：替换而不是追加
            if (
              (category === "hot_content" && type === "short_video") ||
              (category === "hot_content" && type === "picture_content") ||
              (category === "aweme" && type === "Clue")
            ) {
              // 替换现有数据
              this.extractedData[category][type] = [...items];

              // 检查前三名数据
              const topThree = items.filter((item) => item.rank <= 3);
            } else {
              // 其他榜单使用追加模式
              this.extractedData[category][type].push(...items);
            }
          }
        }
      }
    }
  }

  // 更新所有显示
  updateAllDisplays() {
    this.updateDataCounts();
    this.updateRankingDisplay();
  }

  // 更新数据统计
  updateDataCounts() {
    // 更新内容榜计数
    this.updateCategoryCount("hot_content");
    // 更新达人榜计数
    this.updateCategoryCount("aweme");
    // 更新热词榜计数
    this.updateCategoryCount("ranking");
  }

  // 更新分类计数
  updateCategoryCount(category) {
    const data = this.extractedData[category];
    if (!data) return;

    // 计算总数
    const totalCount = Object.values(data).reduce(
      (sum, items) => sum + items.length,
      0
    );
    const totalElement = document.getElementById(`${category}-count-total`);
    if (totalElement) {
      totalElement.textContent = totalCount;
    }

    // 更新子分类计数
    for (const [type, items] of Object.entries(data)) {
      const countElement = document.getElementById(`${category}-${type}-count`);
      if (countElement) {
        countElement.textContent = items.length;
      }
    }
  }

  // 更新榜单显示
  updateRankingDisplay() {
    // 更新各个分类的数据列表
    this.updateCategoryDisplay("hot_content");
    this.updateCategoryDisplay("aweme");
    this.updateCategoryDisplay("ranking");
  }

  // 更新类别显示
  updateCategoryDisplay(category) {
    // 获取各类型的数据，根据不同分类使用不同的数据键名
    let typeData = {};

    if (category === "hot_content") {
      // 获取数据
      let shortVideoData = this.extractedData[category].short_video || [];
      let pictureContentData =
        this.extractedData[category].picture_content || [];
      let productCardData = this.extractedData[category].product_card || [];

      // 为图文榜数据去重
      if (pictureContentData.length > 0) {
        const uniqueItems = new Map();

        // 使用商品ID或内容标题作为唯一标识
        pictureContentData.forEach((item) => {
          const uniqueKey =
            (item.product && item.product.id) ||
            (item.content && item.content.title) ||
            JSON.stringify({
              rank: item.rank,
              title: (item.content && item.content.title) || "unknown",
            });

          // 如果有相同键的项目，保留排名较高的
          if (
            !uniqueItems.has(uniqueKey) ||
            (item.rank &&
              uniqueItems.get(uniqueKey).rank &&
              item.rank < uniqueItems.get(uniqueKey).rank)
          ) {
            uniqueItems.set(uniqueKey, item);
          }
        });

        // 将去重后的数据转回数组
        pictureContentData = Array.from(uniqueItems.values());

        // 更新原始数据以保持一致性
        this.extractedData[category].picture_content = pictureContentData;
      }

      typeData = {
        short_video: shortVideoData,
        picture_content: pictureContentData,
        product_card: productCardData,
      };

      // 特别检查短视频榜数据
      if (typeData.short_video && typeData.short_video.length > 0) {
        // 检查前三名数据
        const topThree = typeData.short_video
          .filter((item) => item.rank <= 3)
          .sort((a, b) => a.rank - b.rank);
      }
    } else if (category === "aweme") {
      typeData = {
        Retailers: this.extractedData[category].Retailers || [],
        App: this.extractedData[category].App || [],
        Clue: this.extractedData[category].Clue || [],
        ShortVideo: this.extractedData[category].ShortVideo || [],
      };
    } else if (category === "ranking") {
      typeData = {
        hot_word: this.extractedData[category].hot_word || [],
        hot_topic: this.extractedData[category].hot_topic || [],
        marketing_hot_pots:
          this.extractedData[category].marketing_hot_pots || [],
      };

      // 特别检查营销热点榜数据
      if (
        typeData.marketing_hot_pots &&
        typeData.marketing_hot_pots.length > 0
      ) {
      }
    }

    // 更新每个类型的数据列表
    Object.entries(typeData).forEach(([type, data]) => {
      // 确保数据有排名字段
      const dataWithRank = data.map((item) => {
        if (!item.rank && item.rank !== 0) {
          return { ...item, rank: 999 }; // 为没有排名的数据赋予高排名值
        }
        return item;
      });

      // 对数据按照排名进行排序
      const sortedData = [...dataWithRank].sort((a, b) => {
        // 确保排名为数字
        const rankA =
          typeof a.rank === "number" ? a.rank : parseInt(a.rank) || 999;
        const rankB =
          typeof b.rank === "number" ? b.rank : parseInt(b.rank) || 999;
        return rankA - rankB;
      });

      // 特别处理短视频榜
      if (category === "hot_content" && type === "short_video") {
      }

      // 更新对应的列表元素
      const listElem = document.getElementById(`${category}-${type}-list`);
      if (listElem) {
        if (sortedData.length > 0) {
          listElem.innerHTML = sortedData
            .map((item) => this.createRankingItemHTML(item))
            .join("");
        } else {
          const typeName = this.getCategoryTypeName(category, type);
          listElem.innerHTML = `<div class="empty-state">暂无${typeName}数据</div>`;
        }
      }
    });
  }

  // 获取分类类型名称
  getCategoryTypeName(category, type) {
    const nameMap = {
      hot_content: {
        short_video: "短视频榜单",
        picture_content: "图文榜单",
        product_card: "商品卡榜单",
      },
      aweme: {
        Retailers: "电商行业榜",
        App: "应用下载榜",
        Clue: "线索收集榜",
        ShortVideo: "短视频主题榜",
      },
      ranking: {
        hot_word: "热词榜",
        hot_topic: "热点榜",
        marketing_hot_pots: "营销热点榜",
      },
    };
    return nameMap[category]?.[type] || "未知";
  }

  // 创建榜单项HTML
  createRankingItemHTML(item) {
    try {
      // 特别检查营销热点榜数据
      if (item.isMarketingHotPot) {
      }

      const rank = item.rank || 0;
      const rankClass = rank <= 3 ? `rank-${rank}` : "rank-other";
      const itemClass = rank <= 3 ? `top-${rank}` : "";

      // 封面图片处理
      let coverHtml = "";

      // 营销热点榜数据处理
      if (item.isMarketingHotPot) {
        // 营销热点榜显示等级标识，不需要头像框
        const level = item.level || "未知";
        const levelColor = this.getLevelColor(level);
        const levelGradient = this.getLevelGradient(level);

        coverHtml = `
          <div class="item-cover marketing-level-enhanced" style="background: ${levelGradient}; color: white; display: flex; align-items: center; justify-content: center; font-weight: 900; font-size: 18px; text-shadow: 0 2px 4px rgba(0,0,0,0.8); border: 2px solid ${levelColor}; border-radius: 8px;">
            <span class="level-badge">${this.escapeHtml(level)}</span>
          </div>
        `;
      } else if (item.isApp) {
        // 应用下载榜数据 - 显示达人头像
        if (item.influencer && item.influencer.avatar) {
          coverHtml = `
            <div class="item-cover influencer-avatar">
              <img src="${this.escapeHtml(
                item.influencer.avatar
              )}" alt="达人头像" data-error-text="无头像">
            </div>
          `;
        } else {
          coverHtml = `<div class="item-cover no-image">无头像</div>`;
        }
      } else if (item.isRetailers) {
        // 达人榜数据 - 显示达人头像
        if (item.influencer && item.influencer.avatar) {
          coverHtml = `
            <div class="item-cover influencer-avatar">
              <img src="${this.escapeHtml(
                item.influencer.avatar
              )}" alt="达人头像" data-error-text="无头像">
            </div>
          `;
        } else {
          coverHtml = `<div class="item-cover no-image">无头像</div>`;
        }
      } else if (item.isProductCard) {
        // 商品卡榜数据 - 显示商品图片
        if (item.product && item.product.image) {
          coverHtml = `
            <div class="item-cover product-image">
              <img src="${this.escapeHtml(
                item.product.image
              )}" alt="商品图片" data-error-text="无图片">
            </div>
          `;
        } else {
          coverHtml = `<div class="item-cover no-image">无图片</div>`;
        }
      } else if (item.isImageText) {
        // 图文榜数据 - 显示内容图片
        if (item.content && item.content.coverUrl) {
          coverHtml = `
            <div class="item-cover content-image">
              <img src="${this.escapeHtml(
                item.content.coverUrl
              )}" alt="内容图片" data-error-text="无图片">
            </div>
          `;
        } else if (item.content && item.content.imageUrl) {
          coverHtml = `
            <div class="item-cover content-image">
              <img src="${this.escapeHtml(
                item.content.imageUrl
              )}" alt="内容图片" data-error-text="无图片">
            </div>
          `;
        } else {
          coverHtml = `<div class="item-cover no-image">无图片</div>`;
        }
      } else if (item.isShortVideo) {
        // 短视频榜数据 - 显示视频封面
        if (item.content && item.content.coverUrl) {
          coverHtml = `
            <div class="item-cover content-video">
              <img src="${this.escapeHtml(
                item.content.coverUrl
              )}" alt="视频封面" data-error-text="无封面">
            </div>
          `;
        } else if (item.coverUrl) {
          coverHtml = `
            <div class="item-cover content-video">
              <img src="${this.escapeHtml(
                item.coverUrl
              )}" alt="视频封面" data-error-text="无封面">
            </div>
          `;
        } else {
          coverHtml = `<div class="item-cover no-image">无封面</div>`;
        }
      } else if (
        item.content &&
        (item.exposureIndex || item.completionRate || item.interactionRate)
      ) {
        // 检测old.js风格的短视频榜数据 - 有content对象和扁平字段
        if (item.content && item.content.coverUrl) {
          coverHtml = `
            <div class="item-cover content-video">
              <img src="${this.escapeHtml(
                item.content.coverUrl
              )}" alt="视频封面" data-error-text="无封面">
            </div>
          `;
        } else {
          coverHtml = `<div class="item-cover no-image">无封面</div>`;
        }
      } else {
        // 默认：尝试获取任何可用的图片
        if (item.content && item.content.coverUrl) {
          coverHtml = `
            <div class="item-cover">
              <img src="${this.escapeHtml(
                item.content.coverUrl
              )}" alt="封面" data-error-text="无图片">
            </div>
          `;
        } else if (item.coverUrl) {
          coverHtml = `
            <div class="item-cover">
              <img src="${this.escapeHtml(
                item.coverUrl
              )}" alt="封面" data-error-text="无图片">
            </div>
          `;
        } else {
          coverHtml = `<div class="item-cover no-image">无图片</div>`;
        }
      }

      // 标题和内容处理
      let titleHtml = "";
      let contentHtml = "";

      // 营销热点榜内容
      if (item.isMarketingHotPot) {
        titleHtml = `
          <div class="item-title" title="${this.escapeHtml(item.name)}">
            ${this.escapeHtml(item.name)}
          </div>
        `;

        contentHtml = `
          <div class="item-details">
            ${
              item.description
                ? `
              <div class="detail-item">
                <span class="label">描述:</span>
                <span class="value">${this.escapeHtml(item.description)}</span>
              </div>
            `
                : ""
            }
            <div class="detail-item">
              <span class="label">热度值:</span>
              <span class="value">${this.escapeHtml(
                item.hotValue || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">参与店铺:</span>
              <span class="value">${this.escapeHtml(
                item.participatingStores || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">相关商品:</span>
              <span class="value">${this.escapeHtml(
                item.relatedProducts || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">相关视频:</span>
              <span class="value">${this.escapeHtml(
                item.relatedVideos || "未知"
              )}</span>
            </div>
          </div>
        `;
      } else if (item.isApp && item.influencer) {
        // 应用下载榜内容
        titleHtml = `
          <div class="item-title" title="${this.escapeHtml(
            item.influencer.name
          )}">
            ${this.escapeHtml(item.influencer.name)}
          </div>
        `;

        const metrics = item.metrics || {};
        contentHtml = `
          <div class="item-details">
            <div class="detail-item">
              <span class="label">粉丝数:</span>
              <span class="value">${this.escapeHtml(
                item.influencer.fansCount || item.influencer.followers || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">组件点击率:</span>
              <span class="value">${this.escapeHtml(
                metrics.componentClickRate || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">星图视频播放量:</span>
              <span class="value">${this.escapeHtml(
                metrics.videoPlayCount || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">互动率:</span>
              <span class="value">${this.escapeHtml(
                metrics.interactionRate || "未知"
              )}</span>
            </div>
          </div>
        `;
      } else if (item.isClueRanking && item.influencer) {
        // 线索收集榜内容
        titleHtml = `
          <div class="item-title" title="${this.escapeHtml(
            item.influencer.name
          )}">
            ${this.escapeHtml(item.influencer.name)}
          </div>
        `;

        const metrics = item.metrics || {};
        contentHtml = `
          <div class="item-details">
            <div class="detail-item">
              <span class="label">线索收集量:</span>
              <span class="value">${this.escapeHtml(
                metrics.clueCount || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">视频播放量:</span>
              <span class="value">${this.escapeHtml(
                metrics.playCount || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">互动率:</span>
              <span class="value">${this.escapeHtml(
                metrics.interactionRate || "未知"
              )}</span>
            </div>
          </div>
        `;
      } else if (item.isRetailers && item.influencer) {
        // 电商行业榜内容
        titleHtml = `
          <div class="item-title" title="${this.escapeHtml(
            item.influencer.name
          )}">
            ${this.escapeHtml(item.influencer.name)}
          </div>
        `;

        const metrics = item.metrics || {};
        contentHtml = `
          <div class="item-details">
            <div class="detail-item">
              <span class="label">粉丝数:</span>
              <span class="value">${this.escapeHtml(
                item.influencer.fansCount || item.influencer.followers || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">主推类目:</span>
              <span class="value">${this.escapeHtml(
                item.categories && item.categories.length > 0
                  ? item.categories.join(", ")
                  : metrics.mainCategory || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">销售金额:</span>
              <span class="value">${this.escapeHtml(
                metrics.salesAmount || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">场均观看人数:</span>
              <span class="value">${this.escapeHtml(
                metrics.avgViewers || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">人均看播时长:</span>
              <span class="value">${this.escapeHtml(
                metrics.avgWatchTime || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">互动率:</span>
              <span class="value">${this.escapeHtml(
                metrics.interactionRate || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">带货口碑:</span>
              <span class="value">${this.escapeHtml(
                metrics.reputation || "未知"
              )}</span>
            </div>
          </div>
        `;
      } else if (item.isProductCard && item.product) {
        // 商品卡榜内容
        titleHtml = `
          <div class="item-title" title="${this.escapeHtml(
            item.product.title
          )}">
            ${this.escapeHtml(item.product.title)}
          </div>
        `;

        const metrics = item.metrics || {};
        contentHtml = `
          <div class="item-details">
            <div class="detail-item">
              <span class="label">商品ID:</span>
              <span class="value">${this.escapeHtml(
                item.product.id || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">GMV指数:</span>
              <span class="value">${this.escapeHtml(
                metrics.gmvIndex || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">商品售价:</span>
              <span class="value">${this.escapeHtml(
                metrics.price || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">销售量指数:</span>
              <span class="value">${this.escapeHtml(
                metrics.salesIndex || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">点击指数:</span>
              <span class="value">${this.escapeHtml(
                metrics.clickIndex || "未知"
              )}</span>
            </div>
            <div class="detail-item">
              <span class="label">转化率:</span>
              <span class="value">${this.escapeHtml(
                metrics.conversionRate || "未知"
              )}</span>
            </div>
          </div>
        `;
      } else if (item.isImageText) {
        // 图文榜数据 - 专门的展示逻辑
        const content = item.content || {};
        const stats = item.stats || {};
        const product = item.product || {};

        titleHtml = `
          <div class="item-title" title="${this.escapeHtml(
            content.title || "无标题"
          )}">
            ${this.escapeHtml(content.title || "无标题")}
          </div>
        `;

        contentHtml = `
          <div class="item-details">
            ${
              product.title
                ? `
              <div class="detail-item">
                <span class="label">关联商品:</span>
                <span class="value">${this.escapeHtml(product.title)}</span>
              </div>
            `
                : ""
            }
            ${
              product.id
                ? `
              <div class="detail-item">
                <span class="label">商品ID:</span>
                <span class="value">${this.escapeHtml(product.id)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.imageCount
                ? `
              <div class="detail-item">
                <span class="label">图片张数:</span>
                <span class="value">${this.escapeHtml(stats.imageCount)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.exposure
                ? `
              <div class="detail-item">
                <span class="label">曝光量:</span>
                <span class="value">${this.escapeHtml(stats.exposure)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.playRate3s
                ? `
              <div class="detail-item">
                <span class="label">3s完播率:</span>
                <span class="value">${this.escapeHtml(stats.playRate3s)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.playRate5s
                ? `
              <div class="detail-item">
                <span class="label">5s完播率:</span>
                <span class="value">${this.escapeHtml(stats.playRate5s)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.completeRate
                ? `
              <div class="detail-item">
                <span class="label">完播率:</span>
                <span class="value">${this.escapeHtml(
                  stats.completeRate
                )}</span>
              </div>
            `
                : ""
            }
            ${
              stats.likes
                ? `
              <div class="detail-item">
                <span class="label">点赞数:</span>
                <span class="value">${this.escapeHtml(stats.likes)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.favorites
                ? `
              <div class="detail-item">
                <span class="label">收藏数:</span>
                <span class="value">${this.escapeHtml(stats.favorites)}</span>
              </div>
            `
                : ""
            }
          </div>
        `;
      } else if (
        item.isShortVideo ||
        (item.content &&
          (item.exposureIndex || item.completionRate || item.interactionRate))
      ) {
        // 短视频榜数据 - 兼容old.js和新版本的数据结构
        const content = item.content || {};
        const stats = item.stats || {};

        titleHtml = `
          <div class="item-title" title="${this.escapeHtml(
            content.title || item.title || "无标题"
          )}">
            ${this.escapeHtml(content.title || item.title || "无标题")}
          </div>
        `;

        contentHtml = `
          <div class="item-details">
            ${
              item.author
                ? `
              <div class="detail-item">
                <span class="label">作者:</span>
                <span class="value">${this.escapeHtml(item.author)}</span>
              </div>
            `
                : ""
            }
            ${
              content.duration
                ? `
              <div class="detail-item">
                <span class="label">时长:</span>
                <span class="value">${this.escapeHtml(content.duration)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.exposure || item.exposureIndex
                ? `
              <div class="detail-item">
                <span class="label">曝光量指数:</span>
                <span class="value">${this.escapeHtml(
                  stats.exposure || item.exposureIndex
                )}</span>
              </div>
            `
                : ""
            }
            ${
              stats.playRate3s
                ? `
              <div class="detail-item">
                <span class="label">3s完播率:</span>
                <span class="value">${this.escapeHtml(stats.playRate3s)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.playRate5s
                ? `
              <div class="detail-item">
                <span class="label">5s完播率:</span>
                <span class="value">${this.escapeHtml(stats.playRate5s)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.completeRate || item.completionRate
                ? `
              <div class="detail-item">
                <span class="label">完播率指数:</span>
                <span class="value">${this.escapeHtml(
                  stats.completeRate || item.completionRate
                )}</span>
              </div>
            `
                : ""
            }
            ${
              stats.interactionRate || item.interactionRate
                ? `
              <div class="detail-item">
                <span class="label">互动率指数:</span>
                <span class="value">${this.escapeHtml(
                  stats.interactionRate || item.interactionRate
                )}</span>
              </div>
            `
                : ""
            }
            ${
              stats.likes
                ? `
              <div class="detail-item">
                <span class="label">点赞数:</span>
                <span class="value">${this.escapeHtml(stats.likes)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.comments
                ? `
              <div class="detail-item">
                <span class="label">评论数:</span>
                <span class="value">${this.escapeHtml(stats.comments)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.shares
                ? `
              <div class="detail-item">
                <span class="label">分享数:</span>
                <span class="value">${this.escapeHtml(stats.shares)}</span>
              </div>
            `
                : ""
            }
            ${
              stats.favorites
                ? `
              <div class="detail-item">
                <span class="label">收藏数:</span>
                <span class="value">${this.escapeHtml(stats.favorites)}</span>
              </div>
            `
                : ""
            }
            ${
              item.keywords && item.keywords.length > 0
                ? `
              <div class="keywords">
                ${item.keywords
                  .map(
                    (keyword) =>
                      `<span class="keyword">${this.escapeHtml(keyword)}</span>`
                  )
                  .join("")}
              </div>
            `
                : ""
            }
          </div>
        `;
      } else {
        // 其他类型数据的默认处理逻辑
        titleHtml = `
          <div class="item-title" title="${this.escapeHtml(
            item.title || "无标题"
          )}">
            ${this.escapeHtml(item.title || "无标题")}
          </div>
        `;

        contentHtml = `
          <div class="item-details">
            <div class="detail-item">
              <span class="label">类型:</span>
              <span class="value">未知</span>
            </div>
          </div>
        `;
      }

      return `
        <div class="ranking-item ${itemClass}" data-rank="${rank}">
          <div class="item-rank ${rankClass}">
            ${rank}
          </div>
          ${coverHtml}
          <div class="item-info">
            ${titleHtml}
            ${contentHtml}
          </div>
        </div>
      `;
    } catch (error) {
      console.error("创建榜单项HTML失败:", error);
      return `
        <div class="ranking-item error">
          <div class="item-rank">?</div>
          <div class="item-cover no-image">错误</div>
          <div class="item-info">
            <div class="item-title">数据解析错误</div>
          </div>
        </div>
      `;
    }
  }

  // HTML转义
  escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  // 导出分类数据
  async exportCategoryData(category, format) {
    try {
      // 使用统一权限管理器进行权限检查
      if (this.permissionManager && this.permissionManager.billingManager) {
        const featureName = this.permissionManager.getFeatureNameByFormat(format);

        // 显示扣费确认弹窗
        const canProceed = await this.permissionManager.billingManager.showBillingPrompt(featureName);
        if (!canProceed) {
          return; // 用户取消操作
        }

        // 检查余额
        const productId = this.permissionManager.billingManager.getProductId();
        const balanceCheck = await this.permissionManager.billingManager.checkBalance(productId, 1);
        if (!balanceCheck.sufficient) {
          throw new Error(`点数不足，当前余额：${balanceCheck.currentBalance}，需要：${balanceCheck.requiredPoints}`);
        }
      } else {
        throw new Error('权限管理器未初始化');
      }
    } catch (error) {
      console.error('导出权限检查失败:', error);
      this.showNotification(error.message, 'error');
      return;
    }

    const data = this.extractedData[category];
    if (!data) {
      this.showNotification("没有可导出的数据", "warning");
      return;
    }

    const allItems = Object.values(data).flat();
    if (allItems.length === 0) {
      this.showNotification("没有可导出的数据", "warning");
      return;
    }

    const filename = `oceanengine_${category}_${
      new Date().toISOString().split("T")[0]
    }`;

    // 确定导出类型
    let exportType = category; // 默认使用分类名称

    // 如果是从特定类型导出的，使用更具体的类型名称
    if (allItems.length > 0 && allItems[0].rankingType) {
      exportType = allItems[0].rankingType;
    }

    // 分析数据来确定具体榜单类型
    if (allItems.some((item) => item.isShortVideo)) {
      exportType = "hot_content_short_video";
    } else if (allItems.some((item) => item.isImageText)) {
      exportType = "hot_content_picture_content";
    } else if (allItems.some((item) => item.isProductCard)) {
      exportType = "hot_content_product_card";
    } else if (allItems.some((item) => item.isRetailers)) {
      exportType = "aweme_Retailers";
    } else if (allItems.some((item) => item.isApp)) {
      exportType = "aweme_App";
    } else if (allItems.some((item) => item.isMarketingHotPot)) {
      exportType = "ranking_marketing_hot_pots";
    }

    // 传递榜单类型信息给导出函数
    const typeInfo = {
      category: category,
      exportType: exportType,
      rankingType: this.rankingType,
    };

    await this.exportData(allItems, filename, format, typeInfo);
  }

  // 导出所有数据
  async exportAllData(format) {
    try {
      // 使用统一权限管理器进行权限检查
      if (this.permissionManager && this.permissionManager.billingManager) {
        const featureName = this.permissionManager.getFeatureNameByFormat(format);

        // 显示扣费确认弹窗
        const canProceed = await this.permissionManager.billingManager.showBillingPrompt(featureName);
        if (!canProceed) {
          return; // 用户取消操作
        }

        // 检查余额
        const productId = this.permissionManager.billingManager.getProductId();
        const balanceCheck = await this.permissionManager.billingManager.checkBalance(productId, 1);
        if (!balanceCheck.sufficient) {
          throw new Error(`点数不足，当前余额：${balanceCheck.currentBalance}，需要：${balanceCheck.requiredPoints}`);
        }
      } else {
        throw new Error('权限管理器未初始化');
      }
    } catch (error) {
      console.error('导出权限检查失败:', error);
      this.showNotification(error.message, 'error');
      return;
    }

    const allData = [];
    let categoryTypeMapping = {};

    for (const [category, types] of Object.entries(this.extractedData)) {
      for (const [type, items] of Object.entries(types)) {
        if (items.length > 0) {
          // 记录每种数据的类型信息
          const typeName = `${category}_${type}`;
          categoryTypeMapping[typeName] = items.length;

          // 将数据添加到汇总数组
          allData.push(...items);
        }
      }
    }

    if (allData.length === 0) {
      this.showNotification("没有可导出的数据", "warning");
      return;
    }

    const filename = `oceanengine_all_data_${
      new Date().toISOString().split("T")[0]
    }`;

    // 传递所有数据的类型信息
    const typeInfo = {
      category: "all",
      exportType: "all_data",
      dataTypes: Object.keys(categoryTypeMapping),
      rankingType: this.rankingType,
    };

    await this.exportData(allData, filename, format, typeInfo);
  }

  // 导出数据
  async exportData(data, filename, format, typeInfo = null) {
    try {
      let content, mimeType, extension;

      // 确定最终的type值
      let finalType = "data"; // 默认值

      if (typeInfo) {
        // 优先使用已经确定的exportType
        if (typeInfo.exportType && typeInfo.exportType !== "data") {
          finalType = typeInfo.exportType;
        }
        // 如果有rankingType，尝试构建type
        else if (
          typeInfo.rankingType &&
          typeInfo.rankingType.category &&
          typeInfo.rankingType.type
        ) {
          finalType = `${typeInfo.rankingType.category}_${typeInfo.rankingType.type}`;
        }
        // 如果只有category，使用category
        else if (typeInfo.category && typeInfo.category !== "all") {
          finalType = typeInfo.category;
        }
      }

      // 根据数据内容再次确认type
      if (finalType === "data" || finalType === "all") {
        if (data.length > 0) {
          // 根据数据特征确定类型
          if (data[0].isShortVideo) {
            finalType = "hot_content_short_video";
          } else if (data[0].isImageText) {
            finalType = "hot_content_picture_content";
          } else if (data[0].isProductCard) {
            finalType = "hot_content_product_card";
          } else if (data[0].isRetailers) {
            finalType = "aweme_Retailers";
          } else if (data[0].isApp) {
            finalType = "aweme_App";
          } else if (data[0].isMarketingHotPot) {
            finalType = "ranking_marketing_hot_pots";
          } else if (data[0].isClueRanking) {
            finalType = "aweme_Clue";
          } else if (data[0].rankingCategory && data[0].rankingType) {
            finalType = `${data[0].rankingCategory}_${data[0].rankingType}`;
          }
        }
      }

      if (format === "csv") {
        content = this.convertToCSV(data);
        mimeType = "text/csv";
        extension = "csv";
      } else if (format === "json") {
        content = JSON.stringify(
          {
            domain: "oceanengine",
            type: finalType,
            data: data,
          },
          null,
          2
        );
        mimeType = "application/json";
        extension = "json";
      } else if (format === "feishu") {
        // 使用正确的数据结构导到多维表格
        const feishuData = {
          domain: "oceanengine",
          type: finalType,
          data: data,
        };
        this.exportToFeiShu(feishuData);
        return; // 提前返回，避免后续的blob操作
      }

      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);

      chrome.downloads.download(
        {
          url: url,
          filename: `${filename}.${extension}`,
          saveAs: true,
        },
        (downloadId) => {
          if (chrome.runtime.lastError) {
            console.error("下载失败:", chrome.runtime.lastError);
            this.showNotification("导出失败", "error");
          } else {
            this.showNotification(
              `数据已导出 (${data.length} 条记录)`,
              "success"
            );
          }
          URL.revokeObjectURL(url);
        }
      );
    } catch (error) {
      console.error("导出数据失败:", error);
      this.showNotification("导出失败", "error");
    }
  }
  //导到多维表格
  async exportToFeiShu(data) {
    // 防重复调用保护
    if (this._isExportingToFeiShu) {
      console.warn("[OceanEngine] 飞书导出正在进行中，忽略重复调用");
      return;
    }

    this._isExportingToFeiShu = true;
    let permissionResult = null;

    try {
      // 使用统一权限管理器进行权限检查（不扣费）
      if (this.permissionManager) {
        // 先显示扣费确认弹窗
        if (this.permissionManager.billingManager) {
          const canProceed = await this.permissionManager.billingManager.showBillingPrompt('exportToFeiShu');
          if (!canProceed) {
            return; // 用户取消操作
          }
        }

        // 直接检查余额，不再调用可能触发弹窗的方法
        if (this.permissionManager.billingManager) {
          const productId = this.permissionManager.billingManager.getProductId();
          const balanceCheck = await this.permissionManager.billingManager.checkBalance(productId, 1);
          if (!balanceCheck.sufficient) {
            throw new Error(`点数不足，当前余额：${balanceCheck.currentBalance}，需要：${balanceCheck.requiredPoints}`);
          }
          permissionResult = { success: true, needsBilling: true };
        } else {
          throw new Error('扣费管理器未初始化');
        }
      } else {
        throw new Error('权限管理器未初始化');
      }

      // 显示加载动画
      this.showLoading("正在导出到多维表格，请稍候...");

      // 获取多维表格URL
      const tableUrl = this.settings.multidimensionalTableUrl;

      if (!tableUrl) {
        this.hideLoading();
        showMessage("请先在设置中配置多维表格URL", "warning");
        // 自动切换到设置页面
        document.querySelector('[data-tab="settings"]').click();
        // 高亮多维表格URL输入框
        const urlInput = document.getElementById("multidimensionalTableUrl");
        if (urlInput) {
          urlInput.style.border = "2px solid #f59e0b";
          urlInput.focus();
          setTimeout(() => {
            urlInput.style.border = "";
          }, 3000);
        }
        return;
      }

      try {
        // 先调用API
        await this.callCozeAPI(tableUrl, data);

        // API调用成功后才扣费
        if (permissionResult && permissionResult.needsBilling && this.permissionManager.billingManager) {
          try {
            await this.permissionManager.billingManager.executeBillingByFeature('exportToFeiShu', 1);
            console.log("✅ [OceanEngine] 飞书导出成功，扣费完成");
          } catch (billingError) {
            console.error("⚠️ [OceanEngine] API调用成功但扣费失败:", billingError);
            // API已经成功了，扣费失败只记录日志，不影响用户体验
          }
        }

        // 导出成功后关闭加载动画
        this.hideLoading();
      } catch (apiError) {
        this.hideLoading();
        console.error("❌ [OceanEngine] API调用失败，不扣费:", apiError);
        throw apiError; // 重新抛出错误，让外层catch处理
      }
    } catch (error) {
      this.hideLoading();
      console.error("导到多维表格失败:", error);
      showMessage("导到多维表格失败: " + error.message, "error");
    } finally {
      // 清除防重复调用标志
      this._isExportingToFeiShu = false;
    }
  }

  // 调用后端API代理
  async callCozeAPI(tableUrl, data) {
    try {
      console.log("🚀 [OceanEngine] 开始调用后端API代理接口");
      console.log("📋 [OceanEngine] 请求参数:", { tableUrl, data });

      // 确保data对象结构正确
      if (!data.domain || !data.type) {
        console.error("数据格式错误，缺少domain或type字段:", data);
        throw new Error("数据格式错误，缺少必要字段");
      }

      // 构建符合要求的数据结构
      const requestData = {
        parameters: {
          url: tableUrl,
          data: JSON.stringify({
            domain: data.domain,
            type: data.type,
            data: data.data,
          }),
          type: data.type,
          domain: data.domain,
        },
        workflow_id: "7509311521795112960",
      };

      console.log("📤 [OceanEngine] 发送到后端的数据:", requestData);

      // 获取API Token
      const apiToken = await this.getApiToken();
      if (!apiToken) {
        throw new Error("未找到API Token，请先登录");
      }

      // 调用后端API代理接口，而不是直接调用Coze API
      const feishuExportUrl = window.pluginConfig?.getProxyEndpoint('/feishu_export')
      const response = await fetch(feishuExportUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiToken  // 添加API Key
        },
        body: JSON.stringify(requestData),
      });

      console.log("📡 [OceanEngine] 收到后端响应:", response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ [OceanEngine] 后端响应错误:", errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }

      const result = await response.json();
      console.log("📥 [OceanEngine] 后端返回结果:", result);

      // 检查后端返回的结果格式
      if (result.code === 200) {
        console.log("✅ [OceanEngine] 飞书导出成功!");
        showMessage("数据已成功导到多维表格", "success");
        return result.data;
      } else {
        console.error("❌ [OceanEngine] 后端返回错误:", result);
        throw new Error(result.msg || "API调用失败");
      }
    } catch (error) {
      console.error("💥 [OceanEngine] 飞书导出异常:", error);
      throw new Error(`飞书导出失败: ${error.message}`);
    }
  }
  // 转换为CSV格式
  convertToCSV(data) {
    if (!data || data.length === 0) return "";

    // 检查数据类型以确定CSV标题行
    const hasApp = data.some((item) => item.isApp);
    const hasRetailers = data.some((item) => item.isRetailers);
    const hasProductCards = data.some((item) => item.isProductCard);
    const hasImageText = data.some((item) => item.isImageText);
    const hasMarketingHotPots = data.some((item) => item.isMarketingHotPot);

    let headers = [];

    if (hasMarketingHotPots) {
      // 营销热点榜的CSV标题
      headers = [
        "排名",
        "热点名称",
        "热点描述",
        "等级",
        "热度值",
        "参与店铺",
        "相关商品",
        "相关视频",
        "获取时间",
        "页面URL",
      ];
    } else if (hasApp) {
      // 应用下载榜的CSV标题
      headers = [
        "排名",
        "达人昵称",
        "粉丝数",
        "组件点击率",
        "星图视频播放量",
        "互动率",
        "头像URL",
        "获取时间",
        "页面URL",
      ];
    } else if (hasRetailers) {
      // 达人榜的CSV标题
      headers = [
        "排名",
        "达人昵称",
        "粉丝数",
        "主推类目",
        "销售金额",
        "场均观看人数",
        "人均看播时长",
        "互动率",
        "带货口碑",
        "头像URL",
        "获取时间",
        "页面URL",
      ];
    } else if (hasProductCards) {
      // 商品卡榜的CSV标题
      headers = [
        "排名",
        "商品标题",
        "商品ID",
        "GMV指数",
        "商品售价",
        "销售量指数",
        "点击指数",
        "转化率",
        "商品图片URL",
        "获取时间",
        "页面URL",
      ];
    } else if (hasImageText) {
      // 图文榜的CSV标题
      headers = [
        "排名",
        "内容标题",
        "商品标题",
        "商品ID",
        "图片数量",
        "曝光量",
        "3s完播率",
        "5s完播率",
        "完播率",
        "获取时间",
        "页面URL",
      ];
    } else {
      // 短视频榜的CSV标题
      headers = [
        "排名",
        "视频标题",
        "曝光量",
        "3s完播率",
        "5s完播率",
        "完播率",
        "点赞数",
        "收藏数",
        "评论数",
        "分享数",
        "获取时间",
        "页面URL",
      ];
    }

    // 添加BOM以支持Excel中文显示
    let csvContent = "\uFEFF" + headers.join(",") + "\n";

    // 转换数据行
    data.forEach((item) => {
      let row = [];

      if (hasMarketingHotPots && item.isMarketingHotPot) {
        // 营销热点榜数据行
        row = [
          item.rank || "",
          this.escapeCsvField(item.name || ""),
          this.escapeCsvField(item.description || ""),
          this.escapeCsvField(item.level || ""),
          this.escapeCsvField(item.hotValue || ""),
          this.escapeCsvField(item.participatingStores || ""),
          this.escapeCsvField(item.relatedProducts || ""),
          this.escapeCsvField(item.relatedVideos || ""),
          new Date(item.timestamp || Date.now()).toLocaleString("zh-CN"),
          this.escapeCsvField(item.pageUrl || ""),
        ];
      } else if (hasApp && item.isApp) {
        // 应用下载榜数据行
        const metrics = item.metrics || {};
        const influencer = item.influencer || {};
        row = [
          item.rank || "",
          this.escapeCsvField(influencer.name || ""),
          this.escapeCsvField(influencer.followers || ""),
          this.escapeCsvField(metrics.componentClickRate || ""),
          this.escapeCsvField(metrics.videoPlayCount || ""),
          this.escapeCsvField(metrics.interactionRate || ""),
          this.escapeCsvField(influencer.avatar || ""),
          new Date(item.timestamp || Date.now()).toLocaleString("zh-CN"),
          this.escapeCsvField(item.pageUrl || ""),
        ];
      } else if (hasRetailers && item.isRetailers) {
        // 达人榜数据行
        const metrics = item.metrics || {};
        const influencer = item.influencer || {};
        row = [
          item.rank || "",
          this.escapeCsvField(influencer.name || ""),
          this.escapeCsvField(influencer.followers || ""),
          this.escapeCsvField(metrics.mainCategory || ""),
          this.escapeCsvField(metrics.salesAmount || ""),
          this.escapeCsvField(metrics.avgViewers || ""),
          this.escapeCsvField(metrics.avgWatchTime || ""),
          this.escapeCsvField(metrics.interactionRate || ""),
          this.escapeCsvField(metrics.reputation || ""),
          this.escapeCsvField(influencer.avatar || ""),
          new Date(item.timestamp || Date.now()).toLocaleString("zh-CN"),
          this.escapeCsvField(item.pageUrl || ""),
        ];
      } else if (hasProductCards && item.isProductCard) {
        // 商品卡榜数据行
        const product = item.product || {};
        const metrics = item.metrics || {};
        row = [
          item.rank || "",
          this.escapeCsvField(product.title || ""),
          this.escapeCsvField(product.id || ""),
          this.escapeCsvField(metrics.gmvIndex || ""),
          this.escapeCsvField(metrics.price || ""),
          this.escapeCsvField(metrics.salesIndex || ""),
          this.escapeCsvField(metrics.clickIndex || ""),
          this.escapeCsvField(metrics.conversionRate || ""),
          this.escapeCsvField(product.image || ""),
          new Date(item.timestamp || Date.now()).toLocaleString("zh-CN"),
          this.escapeCsvField(item.pageUrl || ""),
        ];
      } else if (hasImageText && item.isImageText) {
        // 图文榜数据行
        const content = item.content || {};
        const product = item.product || {};
        const stats = item.stats || {};
        row = [
          item.rank || "",
          this.escapeCsvField(content.title || ""),
          this.escapeCsvField(product.title || ""),
          this.escapeCsvField(product.id || ""),
          stats.imageCount || "",
          this.escapeCsvField(stats.exposure || ""),
          this.escapeCsvField(stats.playRate3s || ""),
          this.escapeCsvField(stats.playRate5s || ""),
          this.escapeCsvField(stats.completeRate || ""),
          new Date(item.timestamp || Date.now()).toLocaleString("zh-CN"),
          this.escapeCsvField(item.pageUrl || ""),
        ];
      } else {
        // 短视频榜数据行
        const stats = item.stats || {};
        row = [
          item.rank || "",
          this.escapeCsvField(item.title || ""),
          this.escapeCsvField(stats.exposure || item.exposureIndex || ""),
          this.escapeCsvField(stats.playRate3s || ""),
          this.escapeCsvField(stats.playRate5s || ""),
          this.escapeCsvField(stats.completeRate || item.completionRate || ""),
          stats.likes || "",
          stats.favorites || "",
          stats.comments || "",
          stats.shares || "",
          new Date(item.timestamp || Date.now()).toLocaleString("zh-CN"),
          this.escapeCsvField(item.pageUrl || ""),
        ];
      }

      csvContent += row.join(",") + "\n";
    });

    return csvContent;
  }

  // 清空分类数据
  async clearCategoryData(category) {
    if (confirm(`确定要清空${this.getCategoryName(category)}数据吗？`)) {
      // 仅清空指定分类的数据

      if (category === "hot_content") {
        // 只清空内容榜数据
        this.extractedData.hot_content = {
          short_video: [],
          picture_content: [],
          product_card: [],
        };

        // 从本地存储中清除内容榜数据
        try {
          const result = await chrome.storage.local.get();
          const keysToRemove = Object.keys(result).filter((key) =>
            key.startsWith(`oceanengine_${category}_`)
          );
          if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
          }
        } catch (error) {
          console.error(`清除 ${category} 本地存储数据失败:`, error);
        }
      } else if (category === "aweme") {
        // 只清空达人榜数据
        this.extractedData.aweme = {
          Retailers: [],
          App: [],
          Clue: [],
          ShortVideo: [],
        };

        // 从本地存储中清除达人榜数据
        try {
          const result = await chrome.storage.local.get();
          const keysToRemove = Object.keys(result).filter((key) =>
            key.startsWith(`oceanengine_${category}_`)
          );
          if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
          }
        } catch (error) {
          console.error(`清除 ${category} 本地存储数据失败:`, error);
        }
      } else if (category === "ranking") {
        // 只清空热词热点榜数据
        this.extractedData.ranking = {
          hot_word: [],
          hot_topic: [],
          marketing_hot_pots: [],
        };

        // 从本地存储中清除热词热点榜数据
        try {
          const result = await chrome.storage.local.get();
          const keysToRemove = Object.keys(result).filter((key) =>
            key.startsWith(`oceanengine_${category}_`)
          );
          if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
          }
        } catch (error) {
          console.error(`清除 ${category} 本地存储数据失败:`, error);
        }
      }

      this.updateAllDisplays();
      this.saveDataToStorage();
      this.showNotification(
        `${this.getCategoryName(category)}数据已清空`,
        "info"
      );
    }
  }

  // 获取分类名称
  getCategoryName(category) {
    const nameMap = {
      hot_content: "内容榜",
      aweme: "达人榜",
      ranking: "热词热点榜",
    };
    return nameMap[category] || "未知";
  }

  // 清空所有数据
  clearAllData() {
    if (confirm("确定要清空所有获取的数据吗？")) {
      this.extractedData = {
        hot_content: { short_video: [], picture_content: [], product_card: [] },
        aweme: { Retailers: [], App: [], Clue: [], ShortVideo: [] },
        ranking: { hot_word: [], hot_topic: [], marketing_hot_pots: [] },
      };
      chrome.storage.local.clear();
      this.updateAllDisplays();
      this.saveDataToStorage();
      this.showNotification("所有数据已清空", "info");
    }
  }

  // 清空所有历史数据
  async clearAllHistory() {
    if (confirm("确定要清空所有历史数据吗？包括存储的数据和当前显示的数据。")) {
      try {
        // 清空当前数据
        this.extractedData = {
          hot_content: {
            short_video: [],
            picture_content: [],
            product_card: [],
          },
          aweme: { Retailers: [], App: [], Clue: [], ShortVideo: [] },
          ranking: { hot_word: [], hot_topic: [], marketing_hot_pots: [] },
        };

        // 清空本地存储
        const result = await chrome.storage.local.get();
        const keysToRemove = Object.keys(result).filter((key) =>
          key.startsWith("oceanengine_")
        );
        if (keysToRemove.length > 0) {
          await chrome.storage.local.remove(keysToRemove);
        }

        // 通知内容脚本清空数据
        if (this.currentTab) {
          chrome.tabs.sendMessage(this.currentTab.id, {
            action: "clearOceanEngineHistory",
          });
        }

        this.updateAllDisplays();
        this.showNotification("所有历史数据已清空", "success");
      } catch (error) {
        console.error("清空历史数据失败:", error);
        this.showNotification("清空历史数据失败", "error");
      }
    }
  }

  // 设置项监听器
  setupSettingsListeners() {
    // 获取所有设置项
    const settingElements = {
      multidimensionalTableUrl: document.getElementById(
        "multidimensionalTableUrl"
      ),
    };

    // 为每个设置项添加监听器
    Object.entries(settingElements).forEach(([key, element]) => {
      if (element) {
        const eventType = element.type === "checkbox" ? "change" : "input";
        element.addEventListener(eventType, () => {
          const value =
            element.type === "checkbox" ? element.checked : element.value;
          this.updateSetting(key, value);
        });
      }
    });
  }

  // 更新设置
  updateSetting(key, value) {
    this.settings[key] = value;
    this.saveSettings();
  }

  // 保存多维表格URL
  saveTableUrl() {
    const urlInput = document.getElementById("multidimensionalTableUrl");
    if (urlInput) {
      this.settings.multidimensionalTableUrl = urlInput.value;
      this.saveSettings();
      this.showNotification("多维表格URL已保存", "success");
    }
  }

  // 加载设置
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get("oceanEngineSettings");
      if (result.oceanEngineSettings) {
        this.settings = { ...this.settings, ...result.oceanEngineSettings };
      }
      this.loadSettingsToUI();
    } catch (error) {
      console.error("加载设置失败:", error);
    }
  }

  // 保存设置
  async saveSettings() {
    try {
      await chrome.storage.sync.set({ oceanEngineSettings: this.settings });
    } catch (error) {
      console.error("保存设置失败:", error);
    }
  }

  // 将设置加载到UI
  loadSettingsToUI() {
    Object.entries(this.settings).forEach(([key, value]) => {
      const element = document.getElementById(key);
      if (element) {
        if (element.type === "checkbox") {
          element.checked = value;
        } else {
          element.value = value;
        }
      }
    });
  }

  // 加载存储的数据
  async loadStoredData() {
    try {
      const result = await chrome.storage.local.get();
      const oceanEngineKeys = Object.keys(result).filter((key) =>
        key.startsWith("oceanengine_")
      );

      // 临时存储图文榜数据用于去重
      const pictureContentItems = new Map();

      oceanEngineKeys.forEach((key) => {
        const storedData = result[key];
        if (storedData && storedData.data) {
          // 如果是图文榜数据，先收集起来进行去重
          if (
            storedData.rankingType &&
            storedData.rankingType.category === "hot_content" &&
            storedData.rankingType.type === "picture_content"
          ) {
            storedData.data.forEach((item) => {
              const uniqueKey =
                (item.product && item.product.id) ||
                (item.content && item.content.title) ||
                JSON.stringify({
                  rank: item.rank,
                  title: (item.content && item.content.title) || "unknown",
                });

              // 如果有相同键的项目，保留排名较高的
              if (
                !pictureContentItems.has(uniqueKey) ||
                (item.rank &&
                  pictureContentItems.get(uniqueKey).rank &&
                  item.rank < pictureContentItems.get(uniqueKey).rank)
              ) {
                pictureContentItems.set(uniqueKey, item);
              }
            });
          } else {
            // 其他类型数据直接添加
            this.addNewData({
              [storedData.rankingType.category]: {
                [storedData.rankingType.type]: storedData.data,
              },
            });
          }
        }
      });

      // 如果有图文榜数据，添加去重后的数据
      if (pictureContentItems.size > 0) {
        const uniquePictureContentData = Array.from(
          pictureContentItems.values()
        );

        this.addNewData({
          hot_content: {
            picture_content: uniquePictureContentData,
          },
        });
      }
    } catch (error) {
      console.error("加载存储数据失败:", error);
    }
  }

  // 保存数据到存储
  async saveDataToStorage() {
    try {
      const storageData = {
        oceanEngineExtractedData: this.extractedData,
        lastUpdated: Date.now(),
      };
      await chrome.storage.local.set(storageData);
    } catch (error) {
      console.error("保存数据失败:", error);
    }
  }

  // 显示通知
  showNotification(message, type = "info") {
    // 创建通知元素
    const notification = document.createElement("div");
    notification.className = `popup-notification ${type}`;
    notification.textContent = message;

    // 样式
    notification.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 10000;
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      max-width: 300px;
      word-wrap: break-word;
      animation: slideInRight 0.3s ease;
    `;

    // 设置背景色
    switch (type) {
      case "success":
        notification.style.background = "#2ed573";
        break;
      case "error":
        notification.style.background = "#ff4757";
        break;
      case "warning":
        notification.style.background = "#ffa502";
        break;
      default:
        notification.style.background = "#3742fa";
    }

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // 切换榜单类型
  switchRankingType(type) {
    // 更新标签状态
    document.querySelectorAll(".type-tab").forEach((tab) => {
      tab.classList.remove("active");
    });
    document.querySelector(`[data-type="${type}"]`).classList.add("active");

    // 显示对应的榜单分类
    document.querySelectorAll(".ranking-category").forEach((category) => {
      category.style.display = "none";
    });
    const targetCategory = document.getElementById(`${type}-ranking`);
    if (targetCategory) {
      targetCategory.style.display = "block";
    }
  }

  // 切换数据列表显示
  toggleDataList(targetId) {
    const element = document.getElementById(targetId);
    if (element) {
      const isHidden = element.style.display === "none";
      element.style.display = isHidden ? "block" : "none";
    }
  }

  // 转义CSV字段
  escapeCsvField(field) {
    if (typeof field === "string") {
      return `"${field.replace(/"/g, '""')}"`;
    } else if (typeof field === "number") {
      return field.toString();
    } else if (typeof field === "boolean") {
      return field ? "1" : "0";
    } else if (typeof field === "object" && field !== null) {
      return `"${field.toString().replace(/"/g, '""')}"`;
    } else {
      throw new Error("不支持的CSV字段类型");
    }
  }

  // 获取等级颜色
  getLevelColor(level) {
    switch (level) {
      case "S级":
        return "#FF6B35"; // 橙红色
      case "A级":
        return "#FF8E53"; // 橙色
      case "B级":
        return "#2A55E5"; // 蓝色
      case "C级":
        return "#6C757D"; // 灰色
      default:
        return "#6C757D"; // 默认灰色
    }
  }

  // 获取等级渐变色
  getLevelGradient(level) {
    switch (level) {
      case "S级":
        return "linear-gradient(135deg, #FF6B35, #F7931E, #FF8E53)"; // 橙红到金橙渐变
      case "A级":
        return "linear-gradient(135deg, #FF8E53, #FFA726, #FFB74D)"; // 橙色到亮橙渐变
      case "B级":
        return "linear-gradient(135deg, #2A55E5, #3F7CFF, #5C94FF)"; // 蓝色渐变
      case "C级":
        return "linear-gradient(135deg, #6C757D, #8E8E93, #A8A8A8)"; // 灰色渐变
      default:
        return "linear-gradient(135deg, #6C757D, #8E8E93)"; // 默认灰色渐变
    }
  }

  // 清空图文榜数据
  async clearPictureContentData() {
    // 清空内存中的数据
    this.extractedData.hot_content.picture_content = [];

    // 清空存储中的数据
    try {
      const result = await chrome.storage.local.get();
      const keysToRemove = Object.keys(result).filter(
        (key) =>
          key.includes("oceanengine_hot_content_picture_content") ||
          (result[key]?.rankingType?.category === "hot_content" &&
            result[key]?.rankingType?.type === "picture_content")
      );

      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        this.showNotification(
          `已清空图文榜数据 (${keysToRemove.length} 条)`,
          "success"
        );
      } else {
        this.showNotification("没有图文榜数据需要清空", "info");
      }

      // 更新显示
      this.updateAllDisplays();
    } catch (error) {
      console.error("清空图文榜数据失败:", error);
      this.showNotification("清空图文榜数据失败", "error");
    }
  }

  // 显示全局加载动画
  showLoading(message = "加载中...") {
    // 如果已存在则不重复添加
    if (document.getElementById("globalLoadingMask")) return;
    const mask = document.createElement("div");
    mask.id = "globalLoadingMask";
    mask.style.cssText = `
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      background: rgba(0,0,0,0.25);
      z-index: 99999;
      display: flex;
      align-items: center;
      justify-content: center;
    `;
    const box = document.createElement("div");
    box.style.cssText = `
      background: rgba(255,255,255,0.95);
      border-radius: 10px;
      padding: 32px 40px;
      box-shadow: 0 2px 16px rgba(0,0,0,0.12);
      display: flex;
      flex-direction: column;
      align-items: center;
    `;
    // loading 动画
    const spinner = document.createElement("div");
    spinner.style.cssText = `
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      animation: spin 1s linear infinite;
      margin-bottom: 18px;
    `;
    spinner.className = "global-loading-spinner";
    // 兼容动画
    const style = document.createElement("style");
    style.innerHTML = `@keyframes spin { 0% { transform: rotate(0deg);} 100% { transform: rotate(360deg);} }`;
    document.head.appendChild(style);
    // 文字
    const text = document.createElement("div");
    text.textContent = message;
    text.style.cssText = "font-size: 16px; color: #333;";
    box.appendChild(spinner);
    box.appendChild(text);
    mask.appendChild(box);
    document.body.appendChild(mask);
  }

  // 隐藏全局加载动画
  hideLoading() {
    const mask = document.getElementById("globalLoadingMask");
    if (mask) mask.remove();
    // 移除动画样式
    const style = document.querySelector("style#globalLoadingSpinnerStyle");
    if (style) style.remove();
  }
  // 设置统一权限管理UI
  setupUnifiedPermissionUI() {
    if (!this.permissionManager) {
      console.error('统一权限管理器未初始化');
      return;
    }

    // 设置Token显示/隐藏切换
    this.permissionManager.setupTokenVisibilityToggle();

    // 设置Token自动保存
    this.permissionManager.setupTokenAutoSave('apiToken', () => {
      this.refreshAccountInfo();
    });

    // 设置刷新账户信息按钮
    const refreshAccountBtn = document.getElementById('refreshAccountBtn');
    if (refreshAccountBtn) {
      refreshAccountBtn.addEventListener('click', () => {
        // 强制刷新，从服务器获取最新数据
        this.refreshAccountInfo(true);
      });
    }

    // 设置清除Token按钮
    const clearTokenBtn = document.getElementById('clearTokenBtn');
    if (clearTokenBtn) {
      clearTokenBtn.addEventListener('click', async () => {
        await this.permissionManager.clearApiToken();
        this.refreshAccountInfo();
      });
    }

    // 加载已保存的Token和多维表格URL
    this.loadSavedSettings();
  }

  // 加载已保存的设置
  async loadSavedSettings() {
    try {
      // 加载API Token
      const result = await chrome.storage.local.get('api_token');
      if (result.api_token) {
        const apiTokenInput = document.getElementById('apiToken');
        if (apiTokenInput) {
          apiTokenInput.value = result.api_token;
        }
        // 延迟获取账户信息
        setTimeout(() => {
          this.refreshAccountInfo();
        }, 1000);
      }

      // 加载多维表格URL
      if (this.settings.multidimensionalTableUrl) {
        const tableUrlInput = document.getElementById('multidimensionalTableUrl');
        if (tableUrlInput) {
          tableUrlInput.value = this.settings.multidimensionalTableUrl;
        }
      }
    } catch (error) {
      console.error('加载已保存设置失败:', error);
    }
  }

  // 刷新账号信息 - 使用统一权限管理器
  async refreshAccountInfo(forceRefresh = false) {
    try {
      if (!this.permissionManager) {
        console.error('统一权限管理器未初始化');
        return;
      }

      // 传递forceRefresh参数，强制从服务器获取最新数据
      const accountInfo = await this.permissionManager.getAccountInfo(forceRefresh);
      this.permissionManager.updateAccountDisplay(accountInfo);
    } catch (error) {
      console.error('刷新账号信息失败:', error);
      const accountInfoElement = document.getElementById('accountInfo');
      if (accountInfoElement) {
        accountInfoElement.style.display = 'none';
      }
    }
  }

  // 获取API Token的辅助方法
  async getApiToken() {
    try {
      const result = await chrome.storage.local.get("api_token");
      return result.api_token || null;
    } catch (error) {
      console.error("获取API令牌失败:", error);
      return null;
    }
  }
}

// 初始化弹窗管理器
const popupManager = new OceanEnginePopupManager();

// 导出到全局作用域供调试使用
window.oceanEnginePopupManager = popupManager;

// showMessage辅助函数
function showMessage(message, type = "info") {
  // 创建通知元素
  const notification = document.createElement("div");
  notification.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 10000;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 12px;
    color: white;
    max-width: 300px;
    word-wrap: break-word;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  `;

  // 设置不同类型的背景色
  switch (type) {
    case "success":
      notification.style.background = "#10b981";
      break;
    case "warning":
      notification.style.background = "#f59e0b";
      break;
    case "error":
      notification.style.background = "#ef4444";
      break;
    default:
      notification.style.background = "#3b82f6";
  }

  notification.textContent = message;
  document.body.appendChild(notification);

  // 3秒后自动移除
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}
