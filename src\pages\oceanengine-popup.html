<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>多小宝（图）</title>
    <link rel="stylesheet" href="../styles/oceanengine.css" />
  </head>
  <body>
    <div class="container oceanengine-theme">
      <!-- 标题栏 -->
      <div class="header">
        <h1 class="title">
          <span class="icon">🚀</span>
          多小宝（图）
          <span class="version">v1.0.1</span>
        </h1>
      </div>

      <!-- 标签导航 -->
      <div class="tabs">
        <button class="tab-btn active" data-tab="extract">获取</button>
        <button class="tab-btn" data-tab="settings">设置</button>
      </div>

      <!-- 获取页面 -->
      <div id="extract" class="tab-content active">
        <!-- 页面状态 -->
        <div class="page-info">
          <div class="page-status">
            <span class="page-type" id="pageType">检测中</span>
            <span class="status-description" id="statusDescription"
              >正在检测巨量引擎页面</span
            >
          </div>
        </div>

        <!-- 获取控制+配置合并为一行紧凑展示 -->
        <div
          class="extract-controls-row"
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            flex-wrap: wrap;
          "
        >
          <button
            id="extractBtn"
            class="btn primary"
            style="height: 28px; font-size: 13px; padding: 2px 12px"
          >
            <span class="btn-icon">🚀</span>
            开始
          </button>
          <button
            id="stopBtn"
            class="btn stop"
            style="
              display: none;
              height: 28px;
              font-size: 13px;
              padding: 2px 12px;
            "
          >
            <span class="btn-icon">⏹️</span>
            停止获取
          </button>
          <label style="margin: 0 6px 0 0; font-size: 13px">
            最大获取数量:
            <input
              type="number"
              id="maxExtractCount"
              value="100"
              min="10"
              max="1000"
              step="10"
              style="
                width: 60px;
                height: 24px;
                font-size: 13px;
                padding: 0 4px;
                margin-left: 2px;
              "
            />
          </label>
          <label
            style="
              margin: 0;
              font-size: 13px;
              display: flex;
              align-items: center;
            "
          >
            <input
              type="checkbox"
              id="autoScroll"
              checked
              style="margin-right: 2px"
            />
            自动滚动加载更多
          </label>
        </div>

        <!-- 飞书多维表格URL输入框（已移到获取页面） -->
        <div class="extract-config" style="width: 100%; padding: 0; margin: 0">
          <div class="config-item" style="width: 100%; padding: 0; margin: 0">
            <label style="width: 100%; display: block">
              多维表格URL(不同的榜单对应不同的URL):
              <input
                type="text"
                id="multidimensionalTableUrl"
                class="url-input"
                placeholder="请输入飞书多维表格URL"
                style="
                  width: 100%;
                  height: 40px;
                  font-size: 13px;
                  padding: 8px;
                  box-sizing: border-box;
                  white-space: nowrap;
                  overflow-x: auto;
                  display: block;
                "
              />
            </label>
          </div>
        </div>

        <!-- 进度显示 -->
        <div
          id="progressDisplay"
          class="progress-display"
          style="display: none"
        >
          <div class="progress-info">
            <span class="progress-text" id="progressText">准备开始...</span>
            <span class="progress-count" id="progressCount">0/0</span>
          </div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              id="progressFill"
              style="width: 0%"
            ></div>
          </div>
        </div>

        <!-- 实时数据展示区域 -->
        <div id="dataDisplay" class="data-display">
          <!-- 榜单类型选择器 -->
          <div class="ranking-type-selector">
            <div class="type-tabs">
              <button class="type-tab active" data-type="hot_content">
                📊 内容榜
              </button>
              <button class="type-tab" data-type="aweme">👤 达人榜</button>
              <button class="type-tab" data-type="ranking">
                🔥 热词热点榜
              </button>
            </div>
          </div>

          <!-- 数据总览 -->
          <div id="dataOverview" class="data-overview">
            <!-- 内容榜数据 -->
            <div
              class="ranking-category"
              id="hot_content-ranking"
              data-type="hot_content"
            >
              <div class="category-header">
                <h4>
                  内容榜数据 (<span id="hot_content-count-total">0</span>)
                </h4>
                <div class="category-actions">
                  <button
                    class="btn small"
                    data-action="export"
                    data-category="hot_content"
                    data-format="feishu"
                  >
                    导到多维表格
                  </button>
                  <button
                    class="btn small"
                    data-action="export"
                    data-category="hot_content"
                    data-format="csv"
                  >
                    导出CSV
                  </button>
                  <button
                    class="btn small"
                    data-action="export"
                    data-category="hot_content"
                    data-format="json"
                  >
                    导出JSON
                  </button>
                  <button
                    class="btn small danger"
                    data-action="clear"
                    data-category="hot_content"
                  >
                    清空
                  </button>
                </div>
              </div>

              <!-- 内容榜子分类 -->
              <div class="sub-categories">
                <!-- 短视频榜单 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>
                      短视频榜单 (<span id="hot_content-short_video-count"
                        >0</span
                      >)
                    </h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="hot_content-short_video-list"
                      >
                        展开/收起
                      </button>
                    </div>
                  </div>
                  <div class="data-list" id="hot_content-short_video-list">
                    <div class="empty-state">暂无短视频榜单数据</div>
                  </div>
                </div>

                <!-- 图文榜单 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>
                      图文榜单 (<span id="hot_content-picture_content-count"
                        >0</span
                      >)
                    </h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="hot_content-picture_content-list"
                      >
                        展开/收起
                      </button>
                      <!-- <button class="btn mini danger" id="clearPictureContentBtn" title="清空图文榜数据">清空图文榜</button> -->
                    </div>
                  </div>
                  <div class="data-list" id="hot_content-picture_content-list">
                    <div class="empty-state">暂无图文榜单数据</div>
                  </div>
                </div>

                <!-- 商品卡榜单 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>
                      商品卡榜单 (<span id="hot_content-product_card-count"
                        >0</span
                      >)
                    </h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="hot_content-product_card-list"
                      >
                        展开/收起
                      </button>
                    </div>
                  </div>
                  <div class="data-list" id="hot_content-product_card-list">
                    <div class="empty-state">暂无商品卡榜单数据</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 达人榜数据 -->
            <div
              class="ranking-category"
              id="aweme-ranking"
              data-type="aweme"
              style="display: none"
            >
              <div class="category-header">
                <h4>达人榜数据 (<span id="aweme-count-total">0</span>)</h4>
                <div class="category-actions">
                  <button
                    class="btn small"
                    data-action="export"
                    data-category="aweme"
                    data-format="feishu"
                  >
                    导到多维表格
                  </button>
                  <button
                    class="btn small"
                    data-action="export"
                    data-category="aweme"
                    data-format="csv"
                  >
                    导出CSV
                  </button>
                  <button
                    class="btn small"
                    data-action="export"
                    data-category="aweme"
                    data-format="json"
                  >
                    导出JSON
                  </button>
                  <button
                    class="btn small danger"
                    data-action="clear"
                    data-category="aweme"
                  >
                    清空
                  </button>
                </div>
              </div>

              <!-- 达人榜子分类 -->
              <div class="sub-categories">
                <!-- 电商行业榜 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>
                      电商行业榜 (<span id="aweme-Retailers-count">0</span>)
                    </h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="aweme-Retailers-list"
                      >
                        展开/收起
                      </button>
                    </div>
                  </div>
                  <div class="data-list" id="aweme-Retailers-list">
                    <div class="empty-state">暂无电商行业榜数据</div>
                  </div>
                </div>

                <!-- 应用下载榜 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>应用下载榜 (<span id="aweme-App-count">0</span>)</h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="aweme-App-list"
                      >
                        展开/收起
                      </button>
                    </div>
                  </div>
                  <div class="data-list" id="aweme-App-list">
                    <div class="empty-state">暂无应用下载榜数据</div>
                  </div>
                </div>

                <!-- 线索收集榜 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>线索收集榜 (<span id="aweme-Clue-count">0</span>)</h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="aweme-Clue-list"
                      >
                        展开/收起
                      </button>
                    </div>
                  </div>
                  <div class="data-list" id="aweme-Clue-list">
                    <div class="empty-state">暂无线索收集榜数据</div>
                  </div>
                </div>

                <!-- 短视频主题榜 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>
                      短视频主题榜 (<span id="aweme-ShortVideo-count">0</span>)
                    </h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="aweme-ShortVideo-list"
                      >
                        展开/收起
                      </button>
                    </div>
                  </div>
                  <div class="data-list" id="aweme-ShortVideo-list">
                    <div class="empty-state">暂无短视频主题榜数据</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 热词热点榜数据 -->
            <div
              class="ranking-category"
              id="ranking-ranking"
              data-type="ranking"
              style="display: none"
            >
              <div class="category-header">
                <h4>
                  热词热点榜数据 (<span id="ranking-count-total">0</span>)
                </h4>
                <div class="category-actions">
                  <button
                    class="btn small"
                    data-action="export"
                    data-category="ranking"
                    data-format="feishu"
                  >
                    导到多维表格
                  </button>
                  <button
                    class="btn small"
                    data-action="export"
                    data-category="ranking"
                    data-format="csv"
                  >
                    导出CSV
                  </button>
                  <button
                    class="btn small"
                    data-action="export"
                    data-category="ranking"
                    data-format="json"
                  >
                    导出JSON
                  </button>
                  <button
                    class="btn small danger"
                    data-action="clear"
                    data-category="ranking"
                  >
                    清空
                  </button>
                </div>
              </div>

              <!-- 热词热点榜子分类 -->
              <div class="sub-categories">
                <!-- 营销热点榜 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>
                      营销热点榜 (<span id="ranking-marketing_hot_pots-count"
                        >0</span
                      >)
                    </h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="ranking-marketing_hot_pots-list"
                      >
                        展开/收起
                      </button>
                    </div>
                  </div>
                  <div class="data-list" id="ranking-marketing_hot_pots-list">
                    <div class="empty-state">暂无营销热点榜数据</div>
                  </div>
                </div>
                <!-- 创作热词榜 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>
                      创作热词榜 (<span id="ranking-hot_word-count">0</span>)
                    </h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="ranking-hot_word-list"
                      >
                        展开/收起
                      </button>
                    </div>
                  </div>
                  <div class="data-list" id="ranking-hot_word-list">
                    <div class="empty-state">暂无创作热词榜数据</div>
                  </div>
                </div>
                <!-- 创作热点榜 -->
                <div class="sub-category">
                  <div class="sub-header">
                    <h5>
                      创作热点榜 (<span id="ranking-hot_topic-count">0</span>)
                    </h5>
                    <div class="sub-actions">
                      <button
                        class="btn mini"
                        data-action="toggle"
                        data-target="ranking-hot_topic-list"
                      >
                        展开/收起
                      </button>
                    </div>
                  </div>
                  <div class="data-list" id="ranking-hot_topic-list">
                    <div class="empty-state">暂无创作热点榜数据</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 设置页面 -->
      <div id="settings" class="tab-content">

        <!-- API管理设置 -->
        <div class="settings-section">
          <h3>🔐 API管理</h3>

          <!-- 账号信息显示 -->
          <div class="account-info" id="accountInfo" style="display: none;">
            <div class="account-card">
              <div class="account-header">
                <span class="account-status" id="accountStatus">✅ 已连接</span>
                <button id="refreshAccountBtn" class="btn-icon" title="刷新账号信息">🔄</button>
              </div>
              <div class="account-details">
                <div class="account-item">
                  <span class="label">用户ID:</span>
                  <span id="accountUserId">-</span>
                </div>
                <div class="account-item">
                  <span class="label">余额:</span>
                  <span id="accountBalance">-</span>
                </div>
                <div class="account-item">
                  <span class="label">会员状态:</span>
                  <span id="accountVipStatus">-</span>
                </div>
              </div>
            </div>
          </div>

          <!-- API Token设置 -->
          <div class="setting-item">
            <label class="setting-label">
              API Token:
              <div class="token-input-group">
                <input
                  type="password"
                  id="apiToken"
                  placeholder="请输入您的API Token"
                  style="flex: 1; margin-right: 8px;"
                />
                <button id="toggleTokenVisibility" class="btn-icon" title="显示/隐藏Token">👁️</button>
              </div>
            </label>
          </div>

          <!-- API操作按钮 -->
          <div class="setting-item">
            <div class="api-actions">
              <button id="clearTokenBtn" class="btn danger">清除Token</button>
            </div>
          </div>
        </div>

        <!-- 多维表格链接 -->
        <div class="settings-section">
          <h3>📊 多维表格链接</h3>
          <div class="setting-item">
            <label class="setting-label">
              URL:
              <input
                type="text"
                id="multidimensionalTableUrl"
                placeholder="请输入飞书多维表格URL"
                style="width: 100%; margin-top: 5px"
              />
            </label>
            <small
              style="
                color: #666;
                font-size: 11px;
                margin-top: 2px;
                display: block;
              "
              >用于数据同步到多维表格平台</small
            >
          </div>
          <div class="setting-item">
            <button id="saveTableUrlBtn" class="btn secondary">💾 保存URL</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载必要的脚本，与抖音页面保持一致 -->
    <script src="../scripts/config.js"></script>
    <script src="../scripts/api-client.js"></script>
    <script src="../scripts/billing-manager.js"></script>
    <script src="../scripts/balance-display.js"></script>
    <!-- 权限管理核心模块 -->
    <script src="../scripts/core/auth-service.js"></script>
    <script src="../scripts/core/permission-config.js"></script>
    <script src="../scripts/core/permission-guard.js"></script>
    <script src="../scripts/oceanengine-popup.js"></script>
  </body>
</html>
