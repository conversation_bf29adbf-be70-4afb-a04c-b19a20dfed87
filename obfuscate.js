// 代码混淆脚本 - 修复版
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// 设置 Node.js 异常处理策略
process.on('uncaughtException', (error) => {
  console.error('捕获到未处理的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('捕获到未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

// 要混淆的文件列表 - 完整版本 - 完整版本
const filesToObfuscate = [
  // 核心配置和API文件
  'src/scripts/config.js',
  'src/scripts/api-client.js',
  'src/scripts/billing-manager.js',
  
  // 权限管理核心文件
  'src/scripts/core/permission-config.js',
  'src/scripts/core/auth-service.js',
  'src/scripts/core/permission-guard.js',

  // 功能模块文件
  'src/scripts/balance-display.js',
  
  // 页面脚本文件
  'src/scripts/background.js',
  'src/scripts/content.js',
  'src/scripts/video-detail.js',
  'src/scripts/popup.js',
  
  // 海洋引擎相关文件
  'src/scripts/oceanengine-content.js',
  'src/scripts/oceanengine-popup.js'
];

// 使用 terser-config.js 配置
const terserConfig = require('./terser-config.js');

// 确保输出目录存在
const outputDir = path.join(__dirname, 'dist');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 确保dist/src/scripts目录存在
const outputScriptsDir = path.join(outputDir, 'src', 'scripts');
if (!fs.existsSync(outputScriptsDir)) {
  fs.mkdirSync(outputScriptsDir, { recursive: true });
}

// 复制非JS文件到dist目录
console.log('复制非JS文件到dist目录...');
fs.copyFileSync(
  path.join(__dirname, 'manifest.json'),
  path.join(outputDir, 'manifest.json')
);

// 复制HTML、CSS和其他静态资源文件
const htmlCssDirs = ['src/pages', 'src/styles'];
htmlCssDirs.forEach(dir => {
  const fullDir = path.join(__dirname, dir);
  const outputFullDir = path.join(outputDir, dir);

  if (!fs.existsSync(outputFullDir)) {
    fs.mkdirSync(outputFullDir, { recursive: true });
  }

  fs.readdirSync(fullDir).forEach(file => {
    const sourcePath = path.join(fullDir, file);
    const destPath = path.join(outputFullDir, file);
    
    // 如果是文件而不是目录，才进行复制
    if (fs.statSync(sourcePath).isFile()) {
      fs.copyFileSync(sourcePath, destPath);
    }
  });
});

// 单独处理图标目录 - 开始
const iconDir = 'src/assets/icons';
const fullIconDir = path.join(__dirname, iconDir);
const outputIconDir = path.join(outputDir, iconDir);

if (fs.existsSync(fullIconDir)) {
  console.log(`复制图标文件到${outputIconDir}...`);
  
  if (!fs.existsSync(outputIconDir)) {
    fs.mkdirSync(outputIconDir, { recursive: true });
  }

  fs.readdirSync(fullIconDir).forEach(file => {
    const sourcePath = path.join(fullIconDir, file);
    const destPath = path.join(outputIconDir, file);
    
    // 如果是文件而不是目录，才进行复制
    if (fs.statSync(sourcePath).isFile()) {
      fs.copyFileSync(sourcePath, destPath);
    }
  });
} else {
  console.warn(`图标目录 ${fullIconDir} 不存在，跳过复制`);
}
// 单独处理图标目录 - 结束

// 复制其他可能需要的静态资源（可选）
const otherAssets = ['src/assets/images', 'src/assets/fonts'];
otherAssets.forEach(dir => {
  const fullDir = path.join(__dirname, dir);
  const outputFullDir = path.join(outputDir, dir);
  
  if (fs.existsSync(fullDir)) {
    if (!fs.existsSync(outputFullDir)) {
      fs.mkdirSync(outputFullDir, { recursive: true });
    }

    fs.readdirSync(fullDir).forEach(file => {
      const sourcePath = path.join(fullDir, file);
      const destPath = path.join(outputFullDir, file);
      
      if (fs.statSync(sourcePath).isFile()) {
        fs.copyFileSync(sourcePath, destPath);
      }
    });
  }
});

// 确保所有必要的目录存在
const requiredDirs = [
  'src/scripts',
  'src/scripts/core',
  'src/pages', 
  'src/styles',
  'src/assets/icons'
];

requiredDirs.forEach(dir => {
  const outputDirPath = path.join(outputDir, dir);
  if (!fs.existsSync(outputDirPath)) {
    fs.mkdirSync(outputDirPath, { recursive: true });
  }
});

// 复制所有src目录下的非JS文件
function copyNonJSFiles(srcDir, destDir) {
  if (!fs.existsSync(srcDir)) return;
  
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
  }

  fs.readdirSync(srcDir).forEach(item => {
    const srcPath = path.join(srcDir, item);
    const destPath = path.join(destDir, item);
    
    if (fs.statSync(srcPath).isDirectory()) {
      copyNonJSFiles(srcPath, destPath);
    } else if (!item.endsWith('.js')) {
      // 只复制非JS文件，JS文件由混淆流程处理
      fs.copyFileSync(srcPath, destPath);
    }
  });
}

// 在复制静态资源部分添加
console.log('复制src目录下的非JS文件...');
copyNonJSFiles(path.join(__dirname, 'src'), path.join(outputDir, 'src'));

// 混淆JS文件
console.log('开始混淆JS文件...');

// 混淆JS文件的改进版本
const processFiles = async () => {
  for (const file of filesToObfuscate) {
    const inputFile = path.join(__dirname, file);
    const outputFile = path.join(outputDir, file);
    const outputDirPath = path.dirname(outputFile);

    // 检查输入文件是否存在
    if (!fs.existsSync(inputFile)) {
      console.warn(`⚠️ 文件不存在，跳过: ${file}`);
      continue;
    }

    // 确保输出目录存在
    if (!fs.existsSync(outputDirPath)) {
      fs.mkdirSync(outputDirPath, { recursive: true });
    }

    console.log(`混淆文件: ${file}`);

    try {
      // 使用 terser 模块而不是命令行
      const terser = require('terser');
      const fileContent = fs.readFileSync(inputFile, 'utf8');

      const result = await terser.minify(fileContent, terserConfig);
      
      if (result.error) {
        throw result.error;
      }

      fs.writeFileSync(outputFile, result.code, 'utf8');
      console.log(`✅ 成功混淆: ${file}`);

    } catch (error) {
      console.error(`❌ 混淆失败: ${file}`);
      console.error(`错误信息: ${error.message}`);
      // 如果混淆失败，复制原文件
      fs.copyFileSync(inputFile, outputFile);
      console.log(`📋 已复制原文件: ${file}`);
    }
  }

  console.log('✅ 混淆完成! 混淆后的文件在dist目录中');
  console.log('提示: 请在Chrome扩展管理页面加载dist目录作为未打包扩展进行测试');
};

// 执行混淆处理
processFiles().catch(err => {
  console.error('混淆过程中发生错误:');
  console.error(err);
});
